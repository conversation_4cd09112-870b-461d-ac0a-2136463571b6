<?php

namespace App\Models;

use App\Enums\Collateral\CollateralStatus;
use App\Traits\UniqueCodeTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphOne;

/**
 * Collateral model for managing collateral information
 */
class Collateral extends BaseModel
{
    use HasFactory, UniqueCodeTrait;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'code',
        'company_id',
        'team_id',
        'selection_customer_type_id',
        'customer_type',
        'selection_type_id',
        'type',
        'name',
        'identity_no',
        'status',
        'remark',
        'company_name',
        'business_registration_no',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'uuid' => 'string',
        'company_id' => 'integer',
        'team_id' => 'integer',
        'selection_customer_type_id' => 'integer',
        'selection_type_id' => 'integer',
        'valuation_amount' => 'decimal:2',
        'valuation_received_date' => 'datetime',
        'land_search_received_date' => 'datetime',
        'status' => CollateralStatus::class,
        'deleted_at' => 'datetime',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'deleted_by' => 'integer',
    ];

    /**
     * Scope to get only collateral.
     */
    public function scopeCollateralOnly(Builder $query): Builder
    {
        return $query->whereNull('loan_customer_collateral_id');
    }

    /**
     * Scope to get only loan collateral.
     */
    public function scopeLoanCollateralOnly(Builder $query): Builder
    {
        return $query->whereNotNull('loan_customer_collateral_id');
    }

    /**
     * Get the company for this collateral.
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Get the team for this collateral.
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * Get the collateral type selection.
     */
    public function customerTypeSelection(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_customer_type_id');
    }

    /**
     * Get the collateral type selection.
     */
    public function typeSelection(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_type_id');
    }

    /**
     * Get the property details for this collateral.
     */
    public function property(): HasOne
    {
        return $this->hasOne(CollateralProperty::class);
    }

    /**
     * Get the valuers for this collateral.
     */
    public function valuers(): HasMany
    {
        return $this->hasMany(CollateralValuer::class);
    }

    /**
     * Get the address for this collateral.
     */
    public function address(): MorphOne
    {
        return $this->morphOne(Address::class, 'addressable');
    }

    /**
     * Get the customer collaterals for this collateral.
     */
    public function customerCollaterals(): HasMany
    {
        return $this->hasMany(CustomerCollateral::class, 'collateral_id');
    }

    /**
     * Get the loan customer collateral for this collateral.
     */
    public function loanCustomerCollateral(): BelongsTo
    {
        return $this->belongsTo(LoanCustomerCollateral::class);
    }

    /**
     * Get collateral accessible to the specified user.
     */
    public static function forUser(?User $user = null): Builder
    {
        $user = $user ?? auth()->user();

        if ($user->isSuperAdmin()) {
            return self::query();
        }

        return self::whereIn('team_id', $user->getAccessibleTeamIds());
    }

    /**
     * The "booting" method of the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->code)) {
                $model->code = self::generateUniqueCode();
            }
        });
    }

    /**
     * Generate a unique code for a new headquarter.
     */
    public static function generateUniqueCode(): string
    {
        return self::generateUniqueCodeWithPrefix(padLength: 5);
    }

    /**
     * The "booted" method of the model.
     */
    // protected static function booted()
    // {
    //     static::created(function ($model) {
    //         $query = CustomerProfile::where('company_id', $model->company_id);

    //         if ($model->selection_customer_type_id == 28) {
    //             $query->where('selection_type_id', 28)
    //                 ->whereRaw('UPPER(name) = ?', [strtoupper($model->name)])
    //                 ->whereRaw('UPPER(identity_no) = ?', [strtoupper($model->identity_no)]);
    //         } elseif ($model->selection_customer_type_id == 29) {
    //             $query->where('selection_type_id', 29)
    //                 ->whereRaw('UPPER(name) = ?', [strtoupper($model->company_name)])
    //                 ->whereRaw('UPPER(identity_no) = ?', [strtoupper($model->business_registration_no)]);
    //         } else {
    //             return;
    //         }

    //         $customer = $query->first();

    //         if ($customer) {
    //             $customer->customerCollaterals()->create([
    //                 'collateral_id' => $model->id,
    //             ]);
    //         }
    //     });
    // }
}
