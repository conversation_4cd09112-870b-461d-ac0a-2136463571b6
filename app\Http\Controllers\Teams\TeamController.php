<?php

namespace App\Http\Controllers\Teams;

use App\Enums\Team\TeamStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\Teams\StoreTeamRequest;
use App\Http\Requests\Teams\UpdateTeamRequest;
use App\Http\Requests\Teams\UpdateTeamStatusRequest;
use App\Http\Resources\Teams\TeamResource;
use App\Models\Company;
use App\Models\Contact;
use App\Models\Headquarter;
use App\Models\Team;
use App\Traits\QueryFilterableTrait;
use App\Traits\SelectionTrait;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Inertia\Response;

class TeamController extends Controller
{
    use QueryFilterableTrait, SelectionTrait;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): Response
    {
        $this->authorize('viewAny', Team::class);

        $query = Team::forUser()
            ->withAuditUsers()
            ->with('address:addressable_id,addressable_type,line_1')
            ->when($request->filled('company_name'), function ($query) use ($request) {
                $query->whereHas('companies', function ($q) use ($request) {
                    $q->where('display_name', 'like', '%'.$request->input('company_name').'%');
                });
            });

        $this->applySearchFilter($query, $request);
        $this->applyStatusFilter($query, $request);
        $this->applySorting($query, $request);

        $teams = $this->applyPagination($query, $request, 10,
            fn ($team) => (new TeamResource($team))->toArray($request));

        return Inertia::render('teams/Index', [
            'teams' => $teams,
            'filters' => $request->only(['name', 'company_name', 'status', 'per_page', 'sort_field', 'sort_direction']),
            'statuses' => TeamStatus::options(),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): Response
    {
        $this->authorize('create', Team::class);

        return Inertia::render('teams/Create', [
            'headquarters' => Headquarter::getForDropdown(),
            'companies' => Company::getForDropdown(),
            'states' => $this->getSelectionsOptionsForCategory('state'),
            'countries' => $this->getSelectionsOptionsForCategory('country'),
            'statuses' => TeamStatus::options(),
            'defaultStatus' => TeamStatus::ACTIVE->value,
            'telephoneCountries' => $this->getSelectionsOptionsForCategory('telephone_country'),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreTeamRequest $request): RedirectResponse
    {
        $this->authorize('create', Team::class);

        $validated = $request->validated();

        try {
            DB::beginTransaction();

            $team = Team::create([
                'uuid' => Str::uuid(),
                'headquarter_id' => $validated['headquarter_id'],
                'company_id' => $validated['company_id'],
                'code' => $validated['code'],
                'name' => $validated['name'],
                'email' => $validated['email'],
                'website' => $validated['website'],
                'status' => $validated['status'],
            ]);

            $team->companies()->attach($validated['company_id']);

            $team->contacts()->create([
                'category' => Contact::CATEGORY_TELEPHONE,
                'selection_country_id' => $validated['selection_telephone_country_id'],
                'contact' => $validated['telephone'],
            ]);

            $team->address()->create([
                'category' => 0,
                'line_1' => $validated['line_1'],
                'line_2' => $validated['line_2'],
                'postcode' => $validated['postcode'],
                'city' => $validated['city'],
                'selection_state_id' => $validated['selection_state_id'],
                'selection_country_id' => $validated['selection_country_id'],
                'is_primary' => true,
            ]);

            DB::commit();

            return Redirect::route('teams.index')->with('success', 'Team created successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create team: '.$e->getMessage());

            return back()->with('error', 'Failed to create team: '.$e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Team $team): Response
    {
        $this->authorize('view', $team);

        $team->withAuditUsers();
        $team->load([
            'address:addressable_id,addressable_type,line_1,line_2,postcode,city,selection_state_id',
            'address.stateSelection:id,value',
            'address.countrySelection:id,value',
            'contacts:contactable_type,contactable_id,selection_country_id,contact',
            'contacts.contactCountrySelection:id,value',
        ]);

        return Inertia::render('teams/Show', [
            'team' => (new TeamResource($team))->toArray(request(), true),
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Team $team): Response
    {
        $this->authorize('update', $team);

        $company = $team->companies->first();

        $team->load([
            'address:addressable_id,addressable_type,line_1,line_2,postcode,city,selection_state_id,selection_country_id',
            'address.stateSelection:id,value',
            'address.countrySelection:id,value',
            'contacts:contactable_type,contactable_id,selection_country_id,contact',
            'contacts.contactCountrySelection:id,value',
        ]);

        return Inertia::render('teams/Edit', [
            'team' => $team,
            'headquarters' => Headquarter::getForDropdown(),
            'companies' => Company::getForDropdown(),
            'states' => $this->getSelectionsOptionsForCategory('state'),
            'countries' => $this->getSelectionsOptionsForCategory('country'),
            'telephoneCountries' => $this->getSelectionsOptionsForCategory('telephone_country'),
            'statuses' => TeamStatus::options(),
            'selectedHeadquarter' => $company?->headquarter_id,
            'selectedCompany' => $company?->id,
            'contact' => $team->telephone() ? [
                'selection_country_id' => $team->telephone()->selection_country_id,
                'contact' => $team->telephone()->contact,
            ] : null,
            'address' => $team->address ? [
                'line_1' => $team->address->line_1,
                'line_2' => $team->address->line_2,
                'postcode' => $team->address->postcode,
                'city' => $team->address->city,
                'selection_state_id' => $team->address->selection_state_id,
                'selection_country_id' => $team->address->selection_country_id,
            ] : null,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateTeamRequest $request, Team $team): RedirectResponse
    {
        $this->authorize('update', $team);

        $validated = $request->validated();

        try {
            DB::beginTransaction();

            $team->update([
                'name' => $validated['name'],
                'email' => $validated['email'],
                'website' => $validated['website'],
                'status' => $validated['status'],
            ]);

            $team->contacts()->updateOrCreate(
                [
                    'contactable_id' => $team->id,
                    'contactable_type' => Team::class,
                ],
                [
                    'category' => Contact::CATEGORY_TELEPHONE,
                    'selection_country_id' => $validated['selection_telephone_country_id'],
                    'contact' => $validated['telephone'],
                ]
            );

            $team->address()->updateOrCreate(
                [
                    'addressable_id' => $team->id,
                    'addressable_type' => Team::class,
                ],
                [
                    'line_1' => $validated['line_1'],
                    'line_2' => $validated['line_2'],
                    'postcode' => $validated['postcode'],
                    'city' => $validated['city'],
                    'selection_state_id' => $validated['selection_state_id'],
                    'selection_country_id' => $validated['selection_country_id'],
                    'is_primary' => true,
                ]
            );

            DB::commit();

            return Redirect::route('teams.index')->with('success', 'Team updated successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update team: '.$e->getMessage());

            return back()->with('error', 'Failed to update team: '.$e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Team $team): RedirectResponse
    {
        $this->authorize('delete', $team);

        try {
            $team->delete();

            return Redirect::route('teams.index')->with('success', 'Team deleted successfully.');
        } catch (\Exception $e) {
            return Redirect::back()->with('error', 'Failed to delete team. '.$e->getMessage());
        }
    }

    /**
     * Update the status of the specified resource.
     */
    public function updateStatus(UpdateTeamStatusRequest $request, Team $team): RedirectResponse
    {
        $this->authorize('update', $team);

        try {
            $team->update(['status' => $request->validated('status')]);

            return Redirect::back()->with('success', 'Team status updated successfully.');
        } catch (\Exception $e) {
            return Redirect::back()->with('error', 'Failed to update team status. '.$e->getMessage());
        }
    }
}
