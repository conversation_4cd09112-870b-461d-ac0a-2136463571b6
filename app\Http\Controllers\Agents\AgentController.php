<?php

namespace App\Http\Controllers\Agents;

use App\Enums\Agent\AgentStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\Agents\StoreAgentRequest;
use App\Http\Requests\Agents\UpdateAgentRequest;
use App\Http\Requests\Agents\UpdateAgentStatusRequest;
use App\Http\Resources\Agents\AgentResource;
use App\Models\AgentProfile;
use App\Models\Company;
use App\Models\Headquarter;
use App\Traits\QueryFilterableTrait;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;
use Inertia\Response;

class AgentController extends Controller
{
    use QueryFilterableTrait;

    /**
     * Display a listing of the agents.
     */
    public function index(Request $request): Response
    {
        $this->authorize('viewAny', AgentProfile::class);

        $query = AgentProfile::forUser()
            ->withAuditUsers()
            ->with(['companies']);

        $this->applySearchFilter($query, $request);
        $this->applyStatusFilter($query, $request);
        $this->applySorting($query, $request);

        $agents = $this->applyPagination($query, $request, 10,
            fn ($agent) => (new AgentResource($agent))->toArray($request));

        return Inertia::render('agents/Index', [
            'agents' => $agents,
            'statuses' => AgentStatus::options(),
            'filters' => $request->only(['name', 'status', 'per_page', 'sort_field', 'sort_direction']),
        ]);
    }

    /**
     * Show the form for creating a new agent.
     */
    public function create(): Response
    {
        $this->authorize('create', AgentProfile::class);

        return Inertia::render('agents/Create', [
            'headquarters' => Headquarter::getForDropdown(),
            'companies' => Company::getForDropdown(),
            'defaultStatus' => AgentStatus::ACTIVE->value,
            'statuses' => AgentStatus::options(),
        ]);
    }

    /**
     * Store a newly created agent in storage.
     */
    public function store(StoreAgentRequest $request): RedirectResponse
    {
        $this->authorize('create', AgentProfile::class);

        try {
            DB::beginTransaction();

            $agent = AgentProfile::create([
                'company_id' => $request->company_id,
                'display_name' => $request->display_name,
                'email' => $request->email,
                'status' => $request->status,
                'remark' => $request->remark,
            ]);

            $agent->companies()->attach($request->company_id);

            DB::commit();

            return Redirect::route('agents.index')->with('success', 'Agent created successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create agent: '.$e->getMessage());

            return Redirect::back()->withInput()->with('error', 'Failed to create agent: '.$e->getMessage());
        }
    }

    /**
     * Display the specified agent.
     */
    public function show(AgentProfile $agent): Response
    {
        $this->authorize('view', $agent);

        $agent->withAuditUsers();

        return Inertia::render('agents/Show', [
            'agent' => (new AgentResource($agent))->toArray(request()),
        ]);
    }

    /**
     * Show the form for editing the specified agent.
     */
    public function edit(AgentProfile $agent): Response
    {
        $this->authorize('update', $agent);

        $agent->load(['teams', 'companies']);

        $company = $agent->companies->first();

        return Inertia::render('agents/Edit', [
            'agent' => [
                'id' => $agent->id,
                'code' => $agent->code,
                'display_name' => $agent->display_name,
                'email' => $agent->email,
                'status' => $agent->status->value,
                'company' => $company?->display_name,
                'headquarter' => $company?->parent?->display_name,
            ],
            'statuses' => AgentStatus::options(),
        ]);
    }

    /**
     * Update the specified agent in storage.
     */
    public function update(UpdateAgentRequest $request, AgentProfile $agent): RedirectResponse
    {
        $this->authorize('update', $agent);

        try {
            DB::beginTransaction();

            $agent->update([
                'email' => $request->email,
                'status' => $request->status,
                'remark' => $request->remark,
            ]);

            DB::commit();

            return Redirect::route('agents.index')->with('success', 'Agent updated successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update agent: '.$e->getMessage());

            return Redirect::back()->withInput()->with('error', 'Failed to update agent: '.$e->getMessage());
        }
    }

    /**
     * Remove the specified agent from storage.
     */
    public function destroy(AgentProfile $agent): RedirectResponse
    {
        $this->authorize('delete', $agent);

        try {
            $agent->delete();

            return Redirect::route('agents.index')->with('success', 'Agent deleted successfully.');
        } catch (\Exception $e) {
            return Redirect::back()->with('error', 'Failed to delete agent: '.$e->getMessage());
        }
    }

    /**
     * Update the status of the specified resource.
     */
    public function updateStatus(UpdateAgentStatusRequest $request, AgentProfile $agent): RedirectResponse
    {
        $this->authorize('update', $agent);

        try {
            $agent->update(['status' => $request->validated('status')]);

            return Redirect::back()->with('success', 'Agent status updated successfully.');
        } catch (\Exception $e) {
            return Redirect::back()->with('error', 'Failed to update agent status: '.$e->getMessage());
        }
    }
}
