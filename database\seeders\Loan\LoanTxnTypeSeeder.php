<?php

namespace Database\Seeders\Loan;

use App\Enums\Loan\LoanTxnType;
use Database\Seeders\VersionedSeeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class LoanTxnTypeSeeder extends VersionedSeeder
{
    protected string $seederName = 'loan_txn_types';

    public function run(): void
    {
        $this->applyVersionedSeeds($this->getVersionedData());
    }

    protected function applyVersion(int $version, array $loanTxnTypes): void
    {
        foreach ($loanTxnTypes as $loanTxnType) {
            DB::table('loan_txn_types')->updateOrInsert(
                [
                    'id' => $loanTxnType['id'],
                ],
                [
                    'uuid' => $loanTxnType['uuid'] ?? Str::uuid(),
                    'type' => $loanTxnType['type'],
                    'sort_order' => $loanTxnType['sort_order'] ?? 0,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            );

            $this->command->line("Created loan transaction type: ID {$loanTxnType['id']} - Type {$loanTxnType['type']}");
        }
    }

    protected function getVersionedData(): array
    {
        return [
            1 => [
                ['id' => 1, 'type' => LoanTxnType::INSTALLMENT->value, 'sort_order' => 900], // Installment
                ['id' => 2, 'type' => LoanTxnType::LATE_INTEREST->value, 'sort_order' => 500], // Late Interest
                ['id' => 3, 'type' => LoanTxnType::LEGAL_FEE->value, 'sort_order' => 600], // Legal Fee
                ['id' => 4, 'type' => LoanTxnType::MISC_CHARGE->value, 'sort_order' => 800], // Misc Charge
                ['id' => 5, 'type' => LoanTxnType::POSTAGE->value, 'sort_order' => 700], // Postage
            ],
        ];
    }
}
