{"containerDefinitions": [{"commonDefault": {"envVar": {"APP_DEBUG": "true", "APP_LOCALE": "en", "APP_FALLBACK_LOCALE": "en", "APP_FAKER_LOCALE": "en_US", "APP_MAINTENANCE_DRIVER": "file", "PHP_CLI_SERVER_WORKERS": "4", "BCRYPT_ROUNDS": "12", "LOG_CHANNEL": "daily", "LOG_STACK": "single", "LOG_DEPRECATIONS_CHANNEL": null, "LOG_LEVEL": "debug", "DB_CONNECTION": "mysql", "DB_PORT": "3306", "SESSION_DRIVER": "database", "SESSION_LIFETIME": "120", "SESSION_ENCRYPT": "false", "SESSION_PATH": "/", "SESSION_DOMAIN": null, "BROADCAST_CONNECTION": "log", "FILESYSTEM_DISK": "s3", "QUEUE_CONNECTION": "database", "CACHE_STORE": "database", "MEMCACHED_HOST": "127.0.0.1", "REDIS_CLIENT": "p<PERSON><PERSON><PERSON>", "REDIS_HOST": "127.0.0.1", "REDIS_PASSWORD": "", "REDIS_PORT": "6379", "AWS_USE_PATH_STYLE_ENDPOINT": "false"}, "secretEnvVar": "APP_KEY,DB_CREDENTIALS,AWS_S3_ACCESS_KEY", "portMappings": {"containerPort": "80"}, "launchType": "EC2", "networkMode": "bridge", "placementConstraints": [{"type": "distinctInstance"}]}, "DEV": {"Custom": {"envVar": {"APP_URL": "https://las-be-dev.devtoz.com", "DB_HOST": "las-dev-2-instance-1.clw8o00wgub9.ap-southeast-1.rds.amazonaws.com", "DB_DATABASE": "las_be", "AWS_BUCKET": "las-dev"}, "resources": {"cpu": 1664, "memory": 768}, "capacityProvider": {"name": "Infra-ECS-Cluster-LAS-DEV-05117fcb-AsgCapacityProvider-dUjfeLGFPsKN", "weight": 1, "base": 1}, "desiredCount": 1, "healthCheckGracePeriodSeconds": 10, "loadBalancerTargetGroupArn": "arn:aws:elasticloadbalancing:ap-southeast-1:711387127071:targetgroup/LAS-DEV/22098b71f1e5fd30"}}, "IQA": {"Custom": {"envVar": {"APP_URL": "https://las-be-iqa.devtoz.com", "DB_HOST": "las-iqa-2.cluster-clw8o00wgub9.ap-southeast-1.rds.amazonaws.com", "DB_DATABASE": "las_iqa", "AWS_BUCKET": "las-iqa"}, "resources": {"cpu": 1664, "memory": 768}, "capacityProvider": {"name": "Infra-ECS-Cluster-LAS-IQA-4f5fe994-AsgCapacityProvider-m3W6eXl3g1ay", "weight": 1, "base": 1}, "desiredCount": 1, "healthCheckGracePeriodSeconds": 10, "loadBalancerTargetGroupArn": "arn:aws:elasticloadbalancing:ap-southeast-1:711387127071:targetgroup/LAS-IQA/3ec63494ecc2ecaa"}}, "QA": {"Custom": {"envVar": {"APP_URL": "https://las-be-qa.devtoz.com", "DB_HOST": "las-qa-2.cluster-clw8o00wgub9.ap-southeast-1.rds.amazonaws.com", "DB_DATABASE": "las_qa", "AWS_BUCKET": "las-qa"}, "resources": {"cpu": 1664, "memory": 768}, "capacityProvider": {"name": "Infra-ECS-Cluster-LAS-QA-3c9c590e-AsgCapacityProvider-Xe95b0VH7VnS", "weight": 1, "base": 1}, "desiredCount": 1, "healthCheckGracePeriodSeconds": 10, "loadBalancerTargetGroupArn": "arn:aws:elasticloadbalancing:ap-southeast-1:711387127071:targetgroup/LAS-QA/81e5bf5a690ce3fe"}}}]}