<?php

namespace App\Http\Controllers\AccessControl;

use App\Http\Controllers\Controller;
use App\Http\Requests\AccessControl\StorePermissionRequest;
use App\Http\Requests\AccessControl\UpdatePermissionRequest;
use App\Http\Resources\AccessControl\PermissionResource;
use App\Traits\QueryFilterableTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Spatie\Permission\Models\Permission;

class PermissionController extends Controller
{
    use QueryFilterableTrait;

    /**
     * Display a listing of the permissions.
     *
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $this->authorize('viewAny', Permission::class);

        $query = Permission::query();

        $this->applySearchFilter($query, $request);
        $this->applySorting($query, $request);

        $permissions = $this->applyPagination($query, $request, 10,
            fn ($permission) => (new PermissionResource($permission))->toArray($request));

        return Inertia::render('permissions/Index', [
            'permissions' => $permissions,
            'filters' => $request->only(['name', 'per_page', 'sort_field', 'sort_direction']),
        ]);
    }

    /**
     * Show the form for creating a new permission.
     *
     * @return \Inertia\Response
     */
    public function create()
    {
        $this->authorize('create', Permission::class);

        return Inertia::render('permissions/Create');
    }

    /**
     * Store a newly created permission in storage.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(StorePermissionRequest $request)
    {
        $this->authorize('create', Permission::class);

        try {
            DB::beginTransaction();

            Permission::create(['name' => $request->name]);

            DB::commit();

            return redirect()->route('permissions.index')->with('message', 'Permission created successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create permission: '.$e->getMessage());

            return redirect()->back()->withInput()->with('error', 'Failed to create permission. '.$e->getMessage());
        }
    }

    /**
     * Display the specified permission.
     *
     * @return \Inertia\Response
     */
    public function show(Permission $permission)
    {
        $this->authorize('view', $permission);

        return Inertia::render('permissions/Show', [
            'permission' => (new PermissionResource($permission))->toArray(request()),
        ]);
    }

    /**
     * Show the form for editing the specified permission.
     *
     * @return \Inertia\Response
     */
    public function edit(Permission $permission)
    {
        $this->authorize('update', $permission);

        return Inertia::render('permissions/Edit', [
            'permission' => $permission,
        ]);
    }

    /**
     * Update the specified permission in storage.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(UpdatePermissionRequest $request, Permission $permission)
    {
        $this->authorize('update', $permission);

        try {
            DB::beginTransaction();

            $permission->update(['name' => $request->name]);

            DB::commit();

            return redirect()->route('permissions.index')->with('message', 'Permission updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update permission: '.$e->getMessage());

            return redirect()->back()->withInput()->with('error', 'Failed to update permission. '.$e->getMessage());
        }
    }

    /**
     * Remove the specified permission from storage.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Permission $permission)
    {
        $this->authorize('delete', $permission);

        try {
            $permission->delete();

            return redirect()->route('permissions.index')->with('message', 'Permission deleted successfully');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to delete permission. '.$e->getMessage());
        }
    }
}
