<?php

namespace App\Http\Controllers\Companies;

use App\Enums\Company\CompanyStatus;
use App\Enums\Company\HeadquarterStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\Companies\StoreHeadquarterRequest;
use App\Http\Requests\Companies\UpdateHeadquarterRequest;
use App\Http\Requests\Companies\UpdateHeadquarterStatusRequest;
use App\Http\Resources\Companies\HeadquarterResource;
use App\Models\Company;
use App\Models\Headquarter;
use App\Traits\HandlesFileStorage;
use App\Traits\QueryFilterableTrait;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Inertia\Response;

class HeadquarterController extends Controller
{
    use HandlesFileStorage, QueryFilterableTrait;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): Response
    {
        $this->authorize('viewAny', Headquarter::class);

        $query = Headquarter::forUser()
            ->withAuditUsers()
            ->withStatus($request);

        $this->applySearchFilter($query, $request);
        $this->applySorting($query, $request);

        $headquarters = $this->applyPagination($query, $request, 10,
            fn ($headquarter) => (new HeadquarterResource($headquarter))->toArray($request));

        return Inertia::render('headquarters/Index', [
            'headquarters' => $headquarters,
            'statuses' => HeadquarterStatus::options(),
            'filters' => $request->only(['name', 'status', 'per_page', 'sort_field', 'sort_direction']),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): Response
    {
        $this->authorize('create', Headquarter::class);

        return Inertia::render('headquarters/Create', [
            'statuses' => CompanyStatus::options(),
            'defaultStatus' => CompanyStatus::ACTIVE->value,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreHeadquarterRequest $request): RedirectResponse
    {
        $this->authorize('create', Headquarter::class);

        try {
            DB::beginTransaction();

            $headquarter = Headquarter::create($request->validated());

            $filePath = $request->logo ? $this->storeUploadedFile($request->logo, 'uploads/'.implode('/', [
                'headquarter',
                $headquarter->uuid,
            ])) : null;

            $headquarter->companies()->create([
                'uuid' => Str::uuid(),
                'name' => $headquarter->name,
                'display_name' => $headquarter->display_name,
                'business_registration_no' => $request->business_registration_no,
                'old_business_registration_no' => $request->old_business_registration_no,
                'is_headquarter' => true,
                'status' => CompanyStatus::ACTIVE,
                'logo' => $filePath,
            ]);

            DB::commit();

            return Redirect::route('headquarters.index')->with('success', 'Headquarter created successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create headquarter: '.$e->getMessage());

            return redirect()->back()->withInput()->with('error', 'Failed to create headquarter. '.$e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Headquarter $headquarter): Response
    {
        $this->authorize('view', $headquarter);

        // Check if user has access to this specific headquarter
        if (! $headquarter->isAccessibleBy()) {
            abort(404);
        }

        $headquarter->load('headquarterCompany');
        $headquarter->withAuditUsers();

        return Inertia::render('headquarters/Show', [
            'headquarter' => (new HeadquarterResource($headquarter))->toArray(request()),
            'logo' => $headquarter->headquarterCompany?->getLogoUrl()['url'] ?? null,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Headquarter $headquarter): Response
    {
        $this->authorize('update', $headquarter);

        $headquarter->load('headquarterCompany');

        return Inertia::render('headquarters/Edit', [
            'headquarter' => $headquarter,
            'logo' => $headquarter->headquarterCompany?->getLogoUrl(),
            'statuses' => CompanyStatus::options(),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateHeadquarterRequest $request, Headquarter $headquarter): RedirectResponse
    {
        $this->authorize('update', $headquarter);

        try {
            DB::beginTransaction();

            $headquarter->update($request->validated());

            $headquarterCompany = $headquarter->headquarterCompany;
            $headquarterCompany->update([
                'business_registration_no' => $request->business_registration_no,
                'old_business_registration_no' => $request->old_business_registration_no,
            ]);

            $storedPath = $this->storeBase64File($request->input('logo'), 'uploads/headquarter/'.$headquarter->uuid, $headquarter->headquarterCompany->logo);

            $headquarterCompany->update([
                'logo' => $storedPath,
            ]);

            DB::commit();

            return Redirect::route('headquarters.index')->with('success', 'Headquarter updated successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update headquarter: '.$e->getMessage());

            return redirect()->back()->withInput()->with('error', 'Failed to update headquarter. '.$e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Headquarter $headquarter): RedirectResponse
    {
        $this->authorize('delete', $headquarter);

        // Headquarters deletion is disabled in the current system architecture
        // This prevents accidental removal of top-level organizational structures
        abort(404, 'Headquarters deletion is not supported');

        // Check if this Headquarter has any child companies
        $hasChildren = Company::where('headquarter_id', $headquarter->id)->exists();

        if ($hasChildren) {
            return Redirect::back()->with('error', 'Cannot delete Headquarter with associated companies. Please remove the associated companies first.');
        }

        try {
            $headquarter->delete();

            return Redirect::route('headquarters.index')->with('success', 'Headquarter deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to delete headquarter. '.$e->getMessage());
        }
    }

    /**
     * Update the status of the specified resource.
     */
    public function updateStatus(UpdateHeadquarterStatusRequest $request, Headquarter $headquarter): RedirectResponse
    {
        $this->authorize('update', $headquarter);

        try {
            $headquarter->update(['status' => $request->validated('status')]);

            return Redirect::back()->with('success', 'Headquarter status updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to update headquarter status. '.$e->getMessage());
        }
    }
}
