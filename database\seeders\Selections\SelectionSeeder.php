<?php

namespace Database\Seeders\Selections;

use App\Enums\Selection\SelectionStatus;
use App\Models\Selection;
use Database\Seeders\VersionedSeeder;

class SelectionSeeder extends VersionedSeeder
{
    protected string $seederName = 'selections';

    public function run(): void
    {
        $this->applyVersionedSeeds($this->getVersionedData());
    }

    protected function applyVersion(int $version, array $selectionGroups): void
    {
        foreach ($selectionGroups as $category => $selections) {
            foreach ($selections as $selection) {
                Selection::firstOrCreate(
                    [
                        'category' => $category,
                        'value' => $selection['value'],
                    ],
                    [
                        'id' => $selection['id'],
                        'description' => $selection['description'] ?? null,
                        'sort_order' => $selection['sort_order'] ?? 0,
                        'status' => $selection['status'] ?? SelectionStatus::ACTIVE,
                    ]
                );

                $this->command->line("Created selection: {$category} - {$selection['value']}");
            }
        }
    }

    protected function getVersionedData(): array
    {
        return [
            1 => [
                'state' => [
                    ['id' => 1, 'value' => 'Johor', 'description' => '', 'sort_order' => 100],
                    ['id' => 2, 'value' => 'Kedah', 'description' => '', 'sort_order' => 100],
                    ['id' => 3, 'value' => 'Kelantan', 'description' => '', 'sort_order' => 100],
                    ['id' => 4, 'value' => 'Melaka', 'description' => '', 'sort_order' => 100],
                    ['id' => 5, 'value' => 'Negeri Sembilan', 'description' => '', 'sort_order' => 100],
                    ['id' => 6, 'value' => 'Pahang', 'description' => '', 'sort_order' => 100],
                    ['id' => 7, 'value' => 'Pulau Pinang', 'description' => '', 'sort_order' => 100],
                    ['id' => 8, 'value' => 'Perak', 'description' => '', 'sort_order' => 100],
                    ['id' => 9, 'value' => 'Perlis', 'description' => '', 'sort_order' => 100],
                    ['id' => 10, 'value' => 'Sabah', 'description' => '', 'sort_order' => 100],
                    ['id' => 11, 'value' => 'Sarawak', 'description' => '', 'sort_order' => 100],
                    ['id' => 12, 'value' => 'Selangor', 'description' => '', 'sort_order' => 100],
                    ['id' => 13, 'value' => 'Terengganu', 'description' => '', 'sort_order' => 100],
                ],
                'collateral_type' => [
                    ['id' => 14, 'value' => 'Property', 'description' => '', 'sort_order' => 100],
                    ['id' => 15, 'value' => 'Other', 'description' => '', 'sort_order' => 100],
                ],
                'land_category' => [
                    ['id' => 16, 'value' => 'Business', 'description' => '', 'sort_order' => 100],
                    ['id' => 17, 'value' => 'Building', 'description' => '', 'sort_order' => 100],
                    ['id' => 18, 'value' => 'Agriculture', 'description' => '', 'sort_order' => 100],
                    ['id' => 19, 'value' => 'Enterprise/Industrial', 'description' => '', 'sort_order' => 100],
                    ['id' => 20, 'value' => 'NA', 'description' => '', 'sort_order' => 100],
                    ['id' => 21, 'value' => 'None', 'description' => '', 'sort_order' => 100],
                    ['id' => 22, 'value' => 'Others', 'description' => '', 'sort_order' => 100],
                ],
                'land_status' => [
                    ['id' => 23, 'value' => 'Freehold', 'description' => '', 'sort_order' => 100],
                    ['id' => 24, 'value' => 'Leasehold', 'description' => '', 'sort_order' => 100],
                ],
                'country' => [
                    ['id' => 25, 'value' => 'Malaysia', 'description' => '', 'sort_order' => 100],
                    ['id' => 26, 'value' => 'Singapore', 'description' => '', 'sort_order' => 100],
                ],
                'property_type' => [
                    ['id' => 27, 'value' => 'House', 'description' => '', 'sort_order' => 100],
                ],
                'customer_type' => [
                    ['id' => 28, 'value' => 'Personal', 'description' => '', 'sort_order' => 100],
                    ['id' => 29, 'value' => 'Business', 'description' => '', 'sort_order' => 100],
                ],
                'square_unit' => [
                    ['id' => 30, 'value' => 'Square Meter (m²)', 'description' => '', 'sort_order' => 100],
                    ['id' => 31, 'value' => 'Square Feet (ft²)', 'description' => '', 'sort_order' => 100],
                ],
                'contact_type' => [
                    ['id' => 32, 'value' => 'Mobile Phone', 'description' => '', 'sort_order' => 100],
                    ['id' => 33, 'value' => 'Telephone', 'description' => '', 'sort_order' => 100],
                ],
                'telephone_country' => [
                    ['id' => 34, 'value' => '+07', 'description' => '', 'sort_order' => 100],
                    ['id' => 35, 'value' => '+03', 'description' => '', 'sort_order' => 100],
                ],
                'mobile_country' => [
                    ['id' => 36, 'value' => '+60', 'description' => '', 'sort_order' => 100],
                    ['id' => 37, 'value' => '+65', 'description' => '', 'sort_order' => 100],
                ],
                'owner_type' => [
                    ['id' => 38, 'value' => 'Director', 'description' => '', 'sort_order' => 100],
                    ['id' => 39, 'value' => 'Shareholder', 'description' => '', 'sort_order' => 100],
                ],
                'address_type' => [
                    ['id' => 40, 'value' => 'Residential', 'description' => '', 'sort_order' => 100],
                    ['id' => 41, 'value' => 'Correspondence', 'description' => '', 'sort_order' => 100],
                ],
                'race' => [
                    ['id' => 42, 'value' => 'Malay', 'description' => '', 'sort_order' => 100],
                    ['id' => 43, 'value' => 'Chinese', 'description' => '', 'sort_order' => 100],
                    ['id' => 44, 'value' => 'Indian', 'description' => '', 'sort_order' => 100],
                    ['id' => 45, 'value' => 'Native', 'description' => '', 'sort_order' => 100],
                    ['id' => 46, 'value' => 'Others', 'description' => '', 'sort_order' => 100],
                ],
                'gender' => [
                    ['id' => 47, 'value' => 'Male', 'description' => '', 'sort_order' => 100],
                    ['id' => 48, 'value' => 'Female', 'description' => '', 'sort_order' => 100],
                ],
                'nationality' => [
                    ['id' => 49, 'value' => 'Malaysian', 'description' => '', 'sort_order' => 100],
                    ['id' => 50, 'value' => 'Singaporean', 'description' => '', 'sort_order' => 100],
                ],
                'education_level' => [
                    ['id' => 51, 'value' => 'No Formal Education', 'description' => '', 'sort_order' => 100],
                    ['id' => 52, 'value' => 'Vocational/Technical Certificate', 'description' => '', 'sort_order' => 100],
                    ['id' => 53, 'value' => 'Doctorate (PhD)', 'description' => '', 'sort_order' => 100],
                    ['id' => 54, 'value' => 'Masters Degree', 'description' => '', 'sort_order' => 100],
                    ['id' => 55, 'value' => 'Bachelors Degree', 'description' => '', 'sort_order' => 100],
                    ['id' => 56, 'value' => 'Diploma', 'description' => '', 'sort_order' => 100],
                    ['id' => 57, 'value' => 'Secondary School', 'description' => '', 'sort_order' => 100],
                    ['id' => 58, 'value' => 'Primary School', 'description' => '', 'sort_order' => 100],
                ],
                'terms_of_employment' => [
                    ['id' => 59, 'value' => 'Contract', 'description' => '', 'sort_order' => 100],
                    ['id' => 60, 'value' => 'Permanent', 'description' => '', 'sort_order' => 100],
                    ['id' => 61, 'value' => 'Self-Employed', 'description' => '', 'sort_order' => 100],
                    ['id' => 62, 'value' => 'Unemployed', 'description' => '', 'sort_order' => 100],
                ],
                'business_classification' => [
                    ['id' => 63, 'value' => 'Partnership', 'description' => '', 'sort_order' => 100],
                    ['id' => 64, 'value' => 'Government', 'description' => '', 'sort_order' => 100],
                    ['id' => 65, 'value' => 'Limited Liability Company Sdn Bhd', 'description' => '', 'sort_order' => 100],
                    ['id' => 66, 'value' => 'Sole Proprietorship', 'description' => '', 'sort_order' => 100],
                    ['id' => 67, 'value' => 'MNC/Public Listed', 'description' => '', 'sort_order' => 100],
                ],
                'occupation' => [
                    ['id' => 68, 'value' => 'ACCOUNTANTS', 'description' => '', 'sort_order' => 100],
                    ['id' => 69, 'value' => 'ACCOUNTING ASSOCIATE PROFESSIONALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 70, 'value' => 'ACTORS', 'description' => '', 'sort_order' => 100],
                    ['id' => 71, 'value' => 'ADMINISTRATION PROFESSIONALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 72, 'value' => 'ADMINISTRATIVE AND COMMERCIAL MANAGERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 73, 'value' => 'ADMINISTRATIVE AND EXECUTIVE SECRETARIES', 'description' => '', 'sort_order' => 100],
                    ['id' => 74, 'value' => 'ADMINISTRATIVE ASSOCIATE PROFESSIONALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 75, 'value' => 'AGRICULTURAL AND INDUSTRIAL MACHINERY MECHANICS AND REPAIRERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 76, 'value' => 'AGRICULTURAL, FORESTRY, FARMING AND FISHERY LABOURERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 77, 'value' => 'AIR CONDITIONING AND REFRIGERATION MECHANICS', 'description' => '', 'sort_order' => 100],
                    ['id' => 78, 'value' => 'AIRCRAFT ENGINE MECHANICS AND REPAIRERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 79, 'value' => 'AIRCRAFT PILOTS AND ELATED PROFESSIONALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 80, 'value' => 'ANIMAL PRODUCERS AND RELATED WORKERS NOT ELSEWHERE CLASSIFIED', 'description' => '', 'sort_order' => 100],
                    ['id' => 81, 'value' => 'ANIMAL-DRAWN MACHINERY AND VEHICLE DRIVERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 82, 'value' => 'ANIMALS KEEPERS AND TRAINERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 83, 'value' => 'ANNOUNCERS ON RADIO, TELEVISION AND OTHER MEDIA', 'description' => '', 'sort_order' => 100],
                    ['id' => 84, 'value' => 'APPLICATIONS PROGRAMMERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 85, 'value' => 'ARMED FORCES OCCUPATIONS', 'description' => '', 'sort_order' => 100],
                    ['id' => 86, 'value' => 'ARTISTIC AND CULTURAL ASSOCIATE PROFESSIONALS NOT ELSEWHERE CLASSIFIED', 'description' => '', 'sort_order' => 100],
                    ['id' => 87, 'value' => 'ASSEMBLERS NOT ELSEWHERE CLASSIFIED', 'description' => '', 'sort_order' => 100],
                    ['id' => 88, 'value' => 'ATHELETES AND SPORTS PLAYERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 89, 'value' => 'AUTHORS AND RELATED WRITERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 90, 'value' => 'BAKERS, PASTRY AND PASTA COOKS, AND CONFECTIONERY MAKERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 91, 'value' => 'BANK TELLERS AND RELATED CLERKS', 'description' => '', 'sort_order' => 100],
                    ['id' => 92, 'value' => 'BEAUTICIANS AND RELATED WORKERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 93, 'value' => 'BICYCLE AND RELATED REPAIRERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 94, 'value' => 'BUILDING AND HOUSEKEEPING SUPERVISORS', 'description' => '', 'sort_order' => 100],
                    ['id' => 95, 'value' => 'BUILDING AND RELATED ELECTRICIANS', 'description' => '', 'sort_order' => 100],
                    ['id' => 96, 'value' => 'BUILDING ARCHITECTS', 'description' => '', 'sort_order' => 100],
                    ['id' => 97, 'value' => 'BUILDING STRUCTURECLEANERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 98, 'value' => 'BUSINESS SERVICES AGENTS', 'description' => '', 'sort_order' => 100],
                    ['id' => 99, 'value' => 'BUYERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 100, 'value' => 'CAR, VAN AND MOTORCYCLE DRIVERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 101, 'value' => 'CARPENTERS AND JOINERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 102, 'value' => 'CARTOGRAPHERS AND SURVEYORS', 'description' => '', 'sort_order' => 100],
                    ['id' => 103, 'value' => 'CASHIERS AND TICKET CLERKS', 'description' => '', 'sort_order' => 100],
                    ['id' => 104, 'value' => 'CHEFS', 'description' => '', 'sort_order' => 100],
                    ['id' => 105, 'value' => 'CHEMISTS', 'description' => '', 'sort_order' => 100],
                    ['id' => 106, 'value' => 'CHILD CARE WORKERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 107, 'value' => 'CIVIL DEFENSE ASSOCIATE PROFESSIONALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 108, 'value' => 'CLEANERS AND HELPERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 109, 'value' => 'CLOWNS, MAGICIANS, ACROBATS AND RELATED PROFESSIONALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 110, 'value' => 'COMMERCIAL SALES AGENT', 'description' => '', 'sort_order' => 100],
                    ['id' => 111, 'value' => 'CONTACT/CALL CENTRE INFORMATION CLERKS', 'description' => '', 'sort_order' => 100],
                    ['id' => 112, 'value' => 'COOKS', 'description' => '', 'sort_order' => 100],
                    ['id' => 113, 'value' => 'CREATIVE AND PERFORMING ARTISTS NOT ELSEWHERE CLASSIFIED', 'description' => '', 'sort_order' => 100],
                    ['id' => 114, 'value' => 'CREDIT AND LOANS OFFICERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 115, 'value' => 'CULTURAL ASSOCIATE PROFESSIONALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 116, 'value' => 'CUSTOMS AND BORDER INSPECTOR ASSOCIATE PROFESSIONALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 117, 'value' => 'DANCERS AND CHOREOGRAPHERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 118, 'value' => 'DATA ENTRY CLERKS', 'description' => '', 'sort_order' => 100],
                    ['id' => 119, 'value' => 'DATABASE DESIGNERS AND ADMINISTRATORS', 'description' => '', 'sort_order' => 100],
                    ['id' => 120, 'value' => 'DENTAL ASSISTANTS AND THERAPISTS', 'description' => '', 'sort_order' => 100],
                    ['id' => 121, 'value' => 'DENTISTS', 'description' => '', 'sort_order' => 100],
                    ['id' => 122, 'value' => 'DISPENSING OPTICIANS', 'description' => '', 'sort_order' => 100],
                    ['id' => 123, 'value' => 'DRIVING INSTRUCTORS', 'description' => '', 'sort_order' => 100],
                    ['id' => 124, 'value' => 'ECONOMISTS', 'description' => '', 'sort_order' => 100],
                    ['id' => 125, 'value' => 'ELECTRICAL LINE INSTALLERS AND REPAIRERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 126, 'value' => 'ELECTRICAL MECHANICS AND FITTERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 127, 'value' => 'ELECTRONICS MECHANICS AND SERVICERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 128, 'value' => 'ENGINEERING PROFESSIONALS (INCLUDING ELECTROTECHNOLOGY)', 'description' => '', 'sort_order' => 100],
                    ['id' => 129, 'value' => 'ENQUIRY CLERKS', 'description' => '', 'sort_order' => 100],
                    ['id' => 130, 'value' => 'FARMING, FORESTRY AND FISHERIES ADVISERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 131, 'value' => 'FASHION AND OTHER MODELS', 'description' => '', 'sort_order' => 100],
                    ['id' => 132, 'value' => 'FILM, STAGE AND RELATED DIRECTORS AND PRODUCERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 133, 'value' => 'FINANCIAL ANALYSTS', 'description' => '', 'sort_order' => 100],
                    ['id' => 134, 'value' => 'FINANCIAL AND INVESTMENT ADVISERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 135, 'value' => 'FIRE-FIGHTERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 136, 'value' => 'FISHERY AND AQUACULTURE PRODUCERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 137, 'value' => 'FITNESS AND RECREATION INSTRUCTORS AND PROGRAM LEADERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 138, 'value' => 'FOOD PREPARATION ASSISTANT', 'description' => '', 'sort_order' => 100],
                    ['id' => 139, 'value' => 'FORESTRY AND RELATED WORKERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 140, 'value' => 'GALLERY, MUSEUM AND LIBRARY TECHNICIANS', 'description' => '', 'sort_order' => 100],
                    ['id' => 141, 'value' => 'GARDENERS, HORTICULTURAL AND NURSERY GROWERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 142, 'value' => 'GENERAL OFFICE CLERKS', 'description' => '', 'sort_order' => 100],
                    ['id' => 143, 'value' => 'GEOLOGISTS AND GEOPHYSICISTS', 'description' => '', 'sort_order' => 100],
                    ['id' => 144, 'value' => 'GOVERNMENT LICENSING OFFICIAL ASSOCIATE PROFESSIONALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 145, 'value' => 'GOVERNMENT SOCIAL BENEFIT OFFICIAL ASSOCIATE PROFESSIONALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 146, 'value' => 'GRAPHIC AND MULTIMEDIA DESIGNERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 147, 'value' => 'HAIRDRESSERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 148, 'value' => 'HAND AND PEDAL VEHICLE DRIVERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 149, 'value' => 'HANDICRAFT WORKERS NOT ELSEWHERE CLASSIFIED', 'description' => '', 'sort_order' => 100],
                    ['id' => 150, 'value' => 'HEALTH ASSOCIATE PROFESSIONALS NOT ELSEWHERE CLASSIFIED', 'description' => '', 'sort_order' => 100],
                    ['id' => 151, 'value' => 'HEALTH CARE ASSISTANTS', 'description' => '', 'sort_order' => 100],
                    ['id' => 152, 'value' => 'HEALTH PROFESSIONALS NOT ELSEWHERE CLASSIFIED', 'description' => '', 'sort_order' => 100],
                    ['id' => 153, 'value' => 'HEAVY TRUCK AND BUS DRIVERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 154, 'value' => 'HOSPITALITY AND RELATED SERVICES PROFESSIONALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 155, 'value' => 'HOSPITALITY, RETAIL AND OTHER SERVICES MANAGERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 156, 'value' => 'HOUSE BUILDERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 157, 'value' => 'HOUSEWIFE/HOUSEHUSBAND', 'description' => '', 'sort_order' => 100],
                    ['id' => 158, 'value' => 'HUNTERS AND TRAPPERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 159, 'value' => 'IMMIGRATION/CUSTOM OFFICERS AND ASSISTANT', 'description' => '', 'sort_order' => 100],
                    ['id' => 160, 'value' => 'INFORMATION AND COMMUNICATIONS TECHNICIANS', 'description' => '', 'sort_order' => 100],
                    ['id' => 161, 'value' => 'INFORMATION AND COMMUNICATIONS TECHNOLOGY INSTALLERS AND SERVICES', 'description' => '', 'sort_order' => 100],
                    ['id' => 162, 'value' => 'INFORMATION AND COMMUNICATIONS TECHNOLOGY MANAGERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 163, 'value' => 'INFORMATION TECHNOLOGY SYSTEM ADMINISTRATORS', 'description' => '', 'sort_order' => 100],
                    ['id' => 164, 'value' => 'INSURANCE AGENT', 'description' => '', 'sort_order' => 100],
                    ['id' => 165, 'value' => 'INTERIOR DESIGNERS AND DECORATORS', 'description' => '', 'sort_order' => 100],
                    ['id' => 166, 'value' => 'JEWELLERY AND PRECIOUS-METAL WORKERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 167, 'value' => 'JOURNALISTS', 'description' => '', 'sort_order' => 100],
                    ['id' => 168, 'value' => 'JUDGES', 'description' => '', 'sort_order' => 100],
                    ['id' => 169, 'value' => 'LANDSCAPE ARCHITECTS', 'description' => '', 'sort_order' => 100],
                    ['id' => 170, 'value' => 'LANGUAGE TEACHERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 171, 'value' => 'LAWYERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 172, 'value' => 'LEGAL ASSOCIATE PROFESSIONALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 173, 'value' => 'LEGAL PROFESSIONALS NOT ELSEWHERE CLASSIFIED', 'description' => '', 'sort_order' => 100],
                    ['id' => 174, 'value' => 'LIBRARIANS, ARCHIVISTS AND CURATORS', 'description' => '', 'sort_order' => 100],
                    ['id' => 175, 'value' => 'LIFE SCIENCE TECHNICIANS AND RELATED ASSOCIATE PROFESSIONALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 176, 'value' => 'LIVESTOCK AND DAIRY PRODUCERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 177, 'value' => 'LOCOMOTIVE ENGINE DRIVERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 178, 'value' => 'MACHINERY MECHANICS AND REPAIRERS NOT ELSEWHERE CLASSIFIED', 'description' => '', 'sort_order' => 100],
                    ['id' => 179, 'value' => 'MACHINE-TOOL SETTER-OPERATORS', 'description' => '', 'sort_order' => 100],
                    ['id' => 180, 'value' => 'MANAGING DIRECTORS AND CHIEF EXECUTIVES', 'description' => '', 'sort_order' => 100],
                    ['id' => 181, 'value' => 'MANUFACTURING LABOURERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 182, 'value' => 'MATHEMATICIANS, ACTUARIES AND STATISTICIANS', 'description' => '', 'sort_order' => 100],
                    ['id' => 183, 'value' => 'MEAT AND FISH PROCESSING WORKERS AND RELATED FOOD PREPARERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 184, 'value' => 'MECHANICAL MACHINERY ASSEMBLERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 185, 'value' => 'MEDICAL AND PHARMACEUTICAL TECHNICIANS', 'description' => '', 'sort_order' => 100],
                    ['id' => 186, 'value' => 'MEDICAL ASSISTANTS', 'description' => '', 'sort_order' => 100],
                    ['id' => 187, 'value' => 'MEDICAL DOCTORS', 'description' => '', 'sort_order' => 100],
                    ['id' => 188, 'value' => 'MEDICAL RECORDS AND HEALTH INFORMATION TECHNICIANS', 'description' => '', 'sort_order' => 100],
                    ['id' => 189, 'value' => 'METAL MOULDERS AND COREMAKERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 190, 'value' => 'METAL WORKING MACHINE TOOL SETTERS AND OPERATORS', 'description' => '', 'sort_order' => 100],
                    ['id' => 191, 'value' => 'METEREOLOGISTS AND SEISMOLOGISTS', 'description' => '', 'sort_order' => 100],
                    ['id' => 192, 'value' => 'MINING AND CONSTRUCTION LABOURERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 193, 'value' => 'MINING, MANUFACTURING AND CONSTRUCTION PROFESSIONALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 194, 'value' => 'MINING, MANUFACTURING AND CONSTRUCTION SUPERVISORS', 'description' => '', 'sort_order' => 100],
                    ['id' => 195, 'value' => 'MIXED CROP AND ANIMAL PRODUCERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 196, 'value' => 'MOBILE PLANT OPERATORS', 'description' => '', 'sort_order' => 100],
                    ['id' => 197, 'value' => 'MOTOR VEHICLE MECHANICS AND REPAIRERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 198, 'value' => 'MUSIC, ARTS AND PERFORMING ARTS TEACHERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 199, 'value' => 'MUSICIANS, SINGERS AND COMPOSERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 200, 'value' => 'NUCLEAR SCIENCE ASSOCIATE PROFESSIONALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 201, 'value' => 'NUMERICAL AND MATERIAL RECORDING CLERKS', 'description' => '', 'sort_order' => 100],
                    ['id' => 202, 'value' => 'NURSING AND MIDWIFERY ASSOCIATE PROFESSIONALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 203, 'value' => 'NURSING AND MIDWIFERY PROFESSIONALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 204, 'value' => 'OPTOMETRISTS AND OPHTHALMIC OPTICIANS', 'description' => '', 'sort_order' => 100],
                    ['id' => 205, 'value' => 'OTHER ADMINISTRATIVE AND SPECIALIZED ASSOCIATE PROFESSIONALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 206, 'value' => 'OTHER BLACKSMITHS, TOOLMAKERS AND RELATED TRADES WORKERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 207, 'value' => 'OTHER BUILDING FINISHERS AND RELATED TRADES WORKERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 208, 'value' => 'OTHER BUILDING FRAME AND RELATED TRADES WORKERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 209, 'value' => 'OTHER CLERICAL SUPPORT WORKERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 210, 'value' => 'OTHER CRAFT AND RELATED WORKERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 211, 'value' => 'OTHER DATABASE AND NETWORK PROFESSIONALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 212, 'value' => 'OTHER FOOD PROCESSING AND RELATED TRADES WORKERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 213, 'value' => 'OTHER GARMENT AND RELATED TRADES WORKERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 214, 'value' => 'OTHER LEGISLATORS AND SENIOR OFFICIAL', 'description' => '', 'sort_order' => 100],
                    ['id' => 215, 'value' => 'OTHER LIFE SCIENCE PROFESSIONALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 216, 'value' => 'OTHER MARKET GARDENERS AND CROP GROWERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 217, 'value' => 'OTHER OUTSIDE LABOUR FORCE', 'description' => '', 'sort_order' => 100],
                    ['id' => 218, 'value' => 'OTHER PRINTING TRADES WORKERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 219, 'value' => 'OTHER SHEET AND STRUCTURAL METAL WORKERS, MOULDERS AND WELDERS AND RELATED WORKERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 220, 'value' => 'OTHER SHIP, AIRCRAFT AND TRAIN/LOCOMOTIVE CONTROLLERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 221, 'value' => 'OTHER SKILLED FISHERY WORKERS, HUNTERS AND TRAPPERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 222, 'value' => 'OTHER TEACHING PROFESSIONALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 223, 'value' => 'OTHER TELLERS, MONEY COLLECTORS AND RELATED CLERKS', 'description' => '', 'sort_order' => 100],
                    ['id' => 224, 'value' => 'OTHER WOOD TREATERS, CABINET-MAKERS AND RELATED TRADES WORKERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 225, 'value' => 'PAINTERS AND RELATED WORKERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 226, 'value' => 'PAWNBROKERS AND MONEY-LENDERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 227, 'value' => 'PERSONAL CARE WORKERS IN HEALTH SERVICES NOT ELSEWHERE CLASSIFIED', 'description' => '', 'sort_order' => 100],
                    ['id' => 228, 'value' => 'PERSONAL SERVICES WORKERS NOT ELSEWHERE CLASSIFIED', 'description' => '', 'sort_order' => 100],
                    ['id' => 229, 'value' => 'PHARMACISTS', 'description' => '', 'sort_order' => 100],
                    ['id' => 230, 'value' => 'PHILOSOPHERS, HISTORIANS AND POLITICAL SCIENTIST', 'description' => '', 'sort_order' => 100],
                    ['id' => 231, 'value' => 'PHOTOGRAPHERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 232, 'value' => 'PHYSICAL AND ENGINEERING SCIENCE TECHNICIANS', 'description' => '', 'sort_order' => 100],
                    ['id' => 233, 'value' => 'PHYSICISTS AND ASTRONOMERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 234, 'value' => 'PHYSIOTHERAPY TECHNICIANS AND ASSISTANTS', 'description' => '', 'sort_order' => 100],
                    ['id' => 235, 'value' => 'PLUMBERS AND PIPE FITTERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 236, 'value' => 'POLICE OFFICERS (REGULATORY GOV.)', 'description' => '', 'sort_order' => 100],
                    ['id' => 237, 'value' => 'POLICE OFFICERS(PROTECTIVE SVC. WORKERS)', 'description' => '', 'sort_order' => 100],
                    ['id' => 238, 'value' => 'PRE-PRESS WORKERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 239, 'value' => 'PRIMARY SCHOOL AND EARLY CHILDHOOD TEACHERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 240, 'value' => 'PRISON GUARDS', 'description' => '', 'sort_order' => 100],
                    ['id' => 241, 'value' => 'PROCESS CONTROL TECHNICIANS', 'description' => '', 'sort_order' => 100],
                    ['id' => 242, 'value' => 'PRODUCT AND GARMENT DESIGNERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 243, 'value' => 'PRODUCTION AND MANUFACTURING MANAGERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 244, 'value' => 'PROFESSIONAL CIVIL DEFENSE OFFICIALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 245, 'value' => 'PROFESSIONAL CUSTOMS AND BORDER INSPECTORS', 'description' => '', 'sort_order' => 100],
                    ['id' => 246, 'value' => 'PROFESSIONAL GOVERNMENT LICENSING OFFICIALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 247, 'value' => 'PROFESSIONAL GOVERNMENT SOCIAL BENEFITS OFFICIALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 248, 'value' => 'PROFESSIONAL POLICE INSPECTORS AND DETECTIVES', 'description' => '', 'sort_order' => 100],
                    ['id' => 249, 'value' => 'PROFESSIONAL TAXATION AND EXCISE OFFICIALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 250, 'value' => 'PROTECTIVE SERVICES WORKERS NOT ELSEWHERE CLASSIFIED', 'description' => '', 'sort_order' => 100],
                    ['id' => 251, 'value' => 'PSYCHOLOGISTS', 'description' => '', 'sort_order' => 100],
                    ['id' => 252, 'value' => 'RAILWAY BRAKE, SIGNAL AND SWITCH OPERATORS', 'description' => '', 'sort_order' => 100],
                    ['id' => 253, 'value' => 'RECEPTIONISTS', 'description' => '', 'sort_order' => 100],
                    ['id' => 254, 'value' => 'REFUSE WORKERS AND OTHER ELEMENTARY WORKERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 255, 'value' => 'REGULATORY GOVERNMENT ASSOCIATE PROFESSIONALS NOT ELSEWHERE CLASSIFIED', 'description' => '', 'sort_order' => 100],
                    ['id' => 256, 'value' => 'REGULATORY GOVERNMENT PROFESSIONALS NOT ELSEWHERE CLASSIFIED', 'description' => '', 'sort_order' => 100],
                    ['id' => 257, 'value' => 'RELIGIOUS PROFESSIONALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 258, 'value' => 'RELIGIOUS TEACHERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 259, 'value' => 'RETIREE', 'description' => '', 'sort_order' => 100],
                    ['id' => 260, 'value' => 'SALES DEMONSTRATORS', 'description' => '', 'sort_order' => 100],
                    ['id' => 261, 'value' => 'SALES WORKERS NOT ELSEWHERE CLASSIFIED', 'description' => '', 'sort_order' => 100],
                    ['id' => 262, 'value' => 'SALES, MARKETING AND PUBLIC RELATIONS PROFESSIONALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 263, 'value' => 'SECONDARY EDUCATION TEACHERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 264, 'value' => 'SECRETARIES (GENERAL)', 'description' => '', 'sort_order' => 100],
                    ['id' => 265, 'value' => 'SECURITIES AND FINANCE DEALERS AND BROKERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 266, 'value' => 'SECURITY GUARDS', 'description' => '', 'sort_order' => 100],
                    ['id' => 267, 'value' => 'SENIOR GOVERNMENT OFFICIALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 268, 'value' => 'SERVICES MANAGERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 269, 'value' => 'SHELF FILLERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 270, 'value' => 'SHIP, AIRCRAFT AND TRAIN TECHNICIANS', 'description' => '', 'sort_order' => 100],
                    ['id' => 271, 'value' => 'SHIPS DECK OFFICERS AND PILOTS', 'description' => '', 'sort_order' => 100],
                    ['id' => 272, 'value' => 'SHIPS ENGINEERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 273, 'value' => 'SHIPS DECK CREWS AND RELATED WORKERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 274, 'value' => 'SHOP KEEPERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 275, 'value' => 'SHOP SALES ASSISTANTS', 'description' => '', 'sort_order' => 100],
                    ['id' => 276, 'value' => 'SHOP SUPERVISORS', 'description' => '', 'sort_order' => 100],
                    ['id' => 277, 'value' => 'SIGN WRITERS, DECORATIVE PAINTERS, ENGRAVERS AND ETCHERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 278, 'value' => 'SOCIAL AND RELIGOUS ASSOCIATE PROFESSIONALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 279, 'value' => 'SOCIAL WORK AND COUNSELING PROFESSIONALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 280, 'value' => 'SOCIOLOGISTS, ANTHROPOLOGISTS AND RELATED PROFESSIONALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 281, 'value' => 'SOFTWARE AND APPLICATIONS DEVELOPERS AND ANALYSTS NOT ELSEWHERE CLASSIFIED', 'description' => '', 'sort_order' => 100],
                    ['id' => 282, 'value' => 'SOFTWARE DEVELOPERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 283, 'value' => 'SPORTS COACHES, INSTRUCTORS AND OFFICIALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 284, 'value' => 'SPRAY PAINTERS AND VARNISHERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 285, 'value' => 'STALL AND MARKET SALESPERSONS', 'description' => '', 'sort_order' => 100],
                    ['id' => 286, 'value' => 'STATIONARY PLANT AND MACHINE OPERATORS', 'description' => '', 'sort_order' => 100],
                    ['id' => 287, 'value' => 'STATISTICAL, MATHEMATICAL AND ACTUARIAL ASSOCIATE PROFESSIONALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 288, 'value' => 'STREET AND RELATED SALES AND SERVICES WORKERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 289, 'value' => 'STREET FOOD SALESPERSONS', 'description' => '', 'sort_order' => 100],
                    ['id' => 290, 'value' => 'STUDENT', 'description' => '', 'sort_order' => 100],
                    ['id' => 291, 'value' => 'SUBSISTENCE FARMERS, FISHERMAN, HUNTERS AND GATHERERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 292, 'value' => 'SYSTEM ANALYSTS', 'description' => '', 'sort_order' => 100],
                    ['id' => 293, 'value' => 'TAILORS, DRESSMAKERS, FURRIERS AND HATTERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 294, 'value' => 'TAXATION AND EXCISE OFFICIAL ASSOCIATE PROFESSIONALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 295, 'value' => 'TEACHERS AIDE', 'description' => '', 'sort_order' => 100],
                    ['id' => 296, 'value' => 'TECHNOLOGY SKILL AND TECHNICAL TRAINERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 297, 'value' => 'TELEPHONE SWITCHBOARD OPERATORS', 'description' => '', 'sort_order' => 100],
                    ['id' => 298, 'value' => 'TOWN AND TRAFFIC PLANNERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 299, 'value' => 'TRADE BROKERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 300, 'value' => 'TRADITIONAL AND COMPLEMENTARY MEDICINE ASSOCIATE PROFESSIONALS', 'description' => '', 'sort_order' => 100],
                    ['id' => 301, 'value' => 'TRANSLATORS, INTERPRETERS AND OTHER LINGUISTS', 'description' => '', 'sort_order' => 100],
                    ['id' => 302, 'value' => 'TRANSPORT AND STORAGE LABOURERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 303, 'value' => 'TRANSPORT CONDUCTORS', 'description' => '', 'sort_order' => 100],
                    ['id' => 304, 'value' => 'TRAVEL ATTENDANTS AND TRAVEL STEWARDS', 'description' => '', 'sort_order' => 100],
                    ['id' => 305, 'value' => 'TRAVEL CONSULTANTS AND RELATED CLERKS', 'description' => '', 'sort_order' => 100],
                    ['id' => 306, 'value' => 'TRAVEL GUIDES', 'description' => '', 'sort_order' => 100],
                    ['id' => 307, 'value' => 'TYPIST AND WORD PROCESSOR OPERATORS', 'description' => '', 'sort_order' => 100],
                    ['id' => 308, 'value' => 'UNIVERSITY AND HIGHER EDUCATION TEACHERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 309, 'value' => 'UPHOLSTERERS AND RELATED WORKERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 310, 'value' => 'VALUERS AND LOSS ASSESSORS', 'description' => '', 'sort_order' => 100],
                    ['id' => 311, 'value' => 'VETERINARY TECHNICIANS AND ASSISTANTS', 'description' => '', 'sort_order' => 100],
                    ['id' => 312, 'value' => 'VISUAL ARTISTS', 'description' => '', 'sort_order' => 100],
                    ['id' => 313, 'value' => 'VOCATIONAL EDUCATION TEACHERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 314, 'value' => 'WAITERS AND BARTENDERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 315, 'value' => 'WEB AND MULTIMEDIA DEVELOPERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 316, 'value' => 'WELDERS AND FLAME CUTTERS', 'description' => '', 'sort_order' => 100],
                    ['id' => 317, 'value' => 'WOODWORKING-MACHINE TOOL SETTERS AND OPERATORS', 'description' => '', 'sort_order' => 100],
                ],
                'marital_status' => [
                    ['id' => 318, 'value' => 'Single', 'description' => '', 'sort_order' => 100],
                    ['id' => 319, 'value' => 'Married', 'description' => '', 'sort_order' => 100],
                    ['id' => 320, 'value' => 'Divorced', 'description' => '', 'sort_order' => 100],
                    ['id' => 321, 'value' => 'Widowed', 'description' => '', 'sort_order' => 100],
                    ['id' => 322, 'value' => 'Separated', 'description' => '', 'sort_order' => 100],
                ],
                'nature_of_business' => [
                    ['id' => 323, 'value' => 'Accounting / Audit / Tax Services', 'description' => '', 'sort_order' => 100],
                    ['id' => 324, 'value' => 'Agriculture', 'description' => '', 'sort_order' => 100],
                    ['id' => 325, 'value' => 'Architecture / Design', 'description' => '', 'sort_order' => 100],
                    ['id' => 326, 'value' => 'Childcare / Kindergarten', 'description' => '', 'sort_order' => 100],
                    ['id' => 327, 'value' => 'Cleaning Services', 'description' => '', 'sort_order' => 100],
                    ['id' => 328, 'value' => 'Clinic / Medical Practice', 'description' => '', 'sort_order' => 100],
                    ['id' => 329, 'value' => 'Construction', 'description' => '', 'sort_order' => 100],
                    ['id' => 330, 'value' => 'Consultancy', 'description' => '', 'sort_order' => 100],
                    ['id' => 331, 'value' => 'Courier / Delivery Services', 'description' => '', 'sort_order' => 100],
                    ['id' => 332, 'value' => 'Digital Marketing', 'description' => '', 'sort_order' => 100],
                    ['id' => 333, 'value' => 'E-commerce / Online Shop', 'description' => '', 'sort_order' => 100],
                    ['id' => 334, 'value' => 'Education / Training', 'description' => '', 'sort_order' => 100],
                    ['id' => 335, 'value' => 'Electrical & Mechanical Services', 'description' => '', 'sort_order' => 100],
                    ['id' => 336, 'value' => 'Electronics Retail', 'description' => '', 'sort_order' => 100],
                    ['id' => 337, 'value' => 'Event Management', 'description' => '', 'sort_order' => 100],
                    ['id' => 338, 'value' => 'Fashion & Apparel', 'description' => '', 'sort_order' => 100],
                    ['id' => 339, 'value' => 'Finance / Credit Services', 'description' => '', 'sort_order' => 100],
                    ['id' => 340, 'value' => 'Fitness / Wellness Center', 'description' => '', 'sort_order' => 100],
                    ['id' => 341, 'value' => 'Food & Beverage', 'description' => '', 'sort_order' => 100],
                    ['id' => 342, 'value' => 'Freight Forwarding', 'description' => '', 'sort_order' => 100],
                    ['id' => 343, 'value' => 'Funeral Services', 'description' => '', 'sort_order' => 100],
                    ['id' => 344, 'value' => 'Furniture', 'description' => '', 'sort_order' => 100],
                    ['id' => 345, 'value' => 'General Trading', 'description' => '', 'sort_order' => 100],
                    ['id' => 346, 'value' => 'Home-based Business', 'description' => '', 'sort_order' => 100],
                    ['id' => 347, 'value' => 'Hotel / Resort', 'description' => '', 'sort_order' => 100],
                    ['id' => 348, 'value' => 'Import / Export', 'description' => '', 'sort_order' => 100],
                    ['id' => 349, 'value' => 'Insurance', 'description' => '', 'sort_order' => 100],
                    ['id' => 350, 'value' => 'Interior Design', 'description' => '', 'sort_order' => 100],
                    ['id' => 351, 'value' => 'Investment Advisory', 'description' => '', 'sort_order' => 100],
                    ['id' => 352, 'value' => 'IT Services', 'description' => '', 'sort_order' => 100],
                    ['id' => 353, 'value' => 'Laundry Services', 'description' => '', 'sort_order' => 100],
                    ['id' => 354, 'value' => 'Legal Services', 'description' => '', 'sort_order' => 100],
                    ['id' => 355, 'value' => 'Logistics', 'description' => '', 'sort_order' => 100],
                    ['id' => 356, 'value' => 'Manufacturing', 'description' => '', 'sort_order' => 100],
                    ['id' => 357, 'value' => 'Medical Equipment Supply', 'description' => '', 'sort_order' => 100],
                    ['id' => 358, 'value' => 'Mini Market / Supermarket', 'description' => '', 'sort_order' => 100],
                    ['id' => 359, 'value' => 'Money Lending', 'description' => '', 'sort_order' => 100],
                    ['id' => 360, 'value' => 'Pet Services', 'description' => '', 'sort_order' => 100],
                    ['id' => 361, 'value' => 'Pharmacy', 'description' => '', 'sort_order' => 100],
                    ['id' => 362, 'value' => 'Poultry / Livestock', 'description' => '', 'sort_order' => 100],
                    ['id' => 363, 'value' => 'Real Estate', 'description' => '', 'sort_order' => 100],
                    ['id' => 364, 'value' => 'Renovation', 'description' => '', 'sort_order' => 100],
                    ['id' => 365, 'value' => 'Restaurant / Café', 'description' => '', 'sort_order' => 100],
                    ['id' => 366, 'value' => 'Retail Business', 'description' => '', 'sort_order' => 100],
                    ['id' => 367, 'value' => 'Salon / Spa', 'description' => '', 'sort_order' => 100],
                    ['id' => 368, 'value' => 'Self-employed / Freelance', 'description' => '', 'sort_order' => 100],
                    ['id' => 369, 'value' => 'Software Development', 'description' => '', 'sort_order' => 100],
                    ['id' => 370, 'value' => 'Textile / Garment', 'description' => '', 'sort_order' => 100],
                    ['id' => 371, 'value' => 'Tourism / Travel Agency', 'description' => '', 'sort_order' => 100],
                    ['id' => 372, 'value' => 'Trading', 'description' => '', 'sort_order' => 100],
                    ['id' => 373, 'value' => 'Traditional Medicine', 'description' => '', 'sort_order' => 100],
                    ['id' => 374, 'value' => 'Training Center', 'description' => '', 'sort_order' => 100],
                    ['id' => 375, 'value' => 'Transportation', 'description' => '', 'sort_order' => 100],
                    ['id' => 376, 'value' => 'Tuition Center', 'description' => '', 'sort_order' => 100],
                    ['id' => 377, 'value' => 'Vehicle Services', 'description' => '', 'sort_order' => 100],
                    ['id' => 378, 'value' => 'Warehousing', 'description' => '', 'sort_order' => 100],
                ],
                'document_type' => [
                    ['id' => 379, 'value' => 'Customer Documents', 'description' => '', 'sort_order' => 100],
                    ['id' => 380, 'value' => 'Collateral Documents', 'description' => '', 'sort_order' => 100],
                    ['id' => 381, 'value' => 'Security Documents', 'description' => '', 'sort_order' => 100],
                ],
                'loan_type' => [
                    ['id' => 382, 'value' => 'Collateral', 'description' => '', 'sort_order' => 100],
                    ['id' => 383, 'value' => 'Non-Collateral', 'description' => '', 'sort_order' => 100],
                ],
                'loan_mode' => [
                    ['id' => 384, 'value' => 'Monthly', 'description' => '', 'sort_order' => 100],
                    ['id' => 385, 'value' => 'Yearly', 'description' => '', 'sort_order' => 100],
                ],
                'repayment_method' => [
                    ['id' => 386, 'value' => 'Equated Monthly Instalment', 'description' => '', 'sort_order' => 100],
                ],
                'relationship' => [
                    ['id' => 387, 'value' => 'Parent', 'description' => '', 'sort_order' => 100],
                    ['id' => 388, 'value' => 'Sibling', 'description' => '', 'sort_order' => 100],
                    ['id' => 389, 'value' => 'Cousin', 'description' => '', 'sort_order' => 100],
                ],
                'transaction_type' => [
                    ['id' => 390, 'value' => 'Official Receipt - Repayment', 'description' => '', 'sort_order' => 100],
                    ['id' => 391, 'value' => 'Postage Charges', 'description' => '', 'sort_order' => 100],
                    ['id' => 392, 'value' => 'Misc Charges', 'description' => '', 'sort_order' => 100],
                    ['id' => 393, 'value' => 'Legal Fee (Letter of Demand)', 'description' => '', 'sort_order' => 100],
                    ['id' => 394, 'value' => 'Repayment + Rebate', 'description' => '', 'sort_order' => 100],
                ],
                'letter_type' => [
                    ['id' => 395, 'value' => 'SMS Reminder', 'description' => '', 'sort_order' => 100],
                    ['id' => 396, 'value' => 'Notice of Demand', 'description' => '', 'sort_order' => 100],
                ],
                'bank_type' => [
                    ['id' => 397, 'value' => 'Affin Bank', 'description' => '', 'sort_order' => 100],
                    ['id' => 398, 'value' => 'Agrobank', 'description' => '', 'sort_order' => 100],
                    ['id' => 399, 'value' => 'Al-Rajhi Bank', 'description' => '', 'sort_order' => 100],
                    ['id' => 400, 'value' => 'Alliance Bank', 'description' => '', 'sort_order' => 100],
                    ['id' => 401, 'value' => 'Ambank', 'description' => '', 'sort_order' => 100],
                    ['id' => 402, 'value' => 'Bangkok Bank', 'description' => '', 'sort_order' => 100],
                    ['id' => 403, 'value' => 'Bank Islam', 'description' => '', 'sort_order' => 100],
                    ['id' => 405, 'value' => 'Bank Kerjasama Rakyat', 'description' => '', 'sort_order' => 100],
                    ['id' => 406, 'value' => 'Bank Muamalat', 'description' => '', 'sort_order' => 100],
                    ['id' => 407, 'value' => 'Bank of America', 'description' => '', 'sort_order' => 100],
                    ['id' => 408, 'value' => 'Bank of China', 'description' => '', 'sort_order' => 100],
                    ['id' => 409, 'value' => 'Bank Simpanan Nasional', 'description' => '', 'sort_order' => 100],
                    ['id' => 410, 'value' => 'BNP Paribas', 'description' => '', 'sort_order' => 100],
                    ['id' => 411, 'value' => 'China Construction Bank', 'description' => '', 'sort_order' => 100],
                    ['id' => 412, 'value' => 'CIMB Bank', 'description' => '', 'sort_order' => 100],
                    ['id' => 413, 'value' => 'Deutsche Bank', 'description' => '', 'sort_order' => 100],
                    ['id' => 414, 'value' => 'Hong Leong Bank', 'description' => '', 'sort_order' => 100],
                    ['id' => 415, 'value' => 'HSBC Bank', 'description' => '', 'sort_order' => 100],
                    ['id' => 416, 'value' => 'Industrial and Commercial Bank of China', 'description' => '', 'sort_order' => 100],
                    ['id' => 417, 'value' => 'J.P Morgan Chase Bank Berhad', 'description' => '', 'sort_order' => 100],
                    ['id' => 418, 'value' => 'Maybank', 'description' => '', 'sort_order' => 100],
                    ['id' => 419, 'value' => 'MBSB Bank', 'description' => '', 'sort_order' => 100],
                    ['id' => 420, 'value' => 'OCBC Bank', 'description' => '', 'sort_order' => 100],
                    ['id' => 421, 'value' => 'Public Bank', 'description' => '', 'sort_order' => 100],
                    ['id' => 422, 'value' => 'RHB Bank', 'description' => '', 'sort_order' => 100],
                    ['id' => 423, 'value' => 'Standard Chartered Bank', 'description' => '', 'sort_order' => 100],
                    ['id' => 424, 'value' => 'United Overseas Bank', 'description' => '', 'sort_order' => 100],
                ],
                'bank_account_type' => [
                    ['id' => 425, 'value' => 'Savings', 'description' => '', 'sort_order' => 100],
                    ['id' => 426, 'value' => 'Current', 'description' => '', 'sort_order' => 100],
                    ['id' => 427, 'value' => 'Joint', 'description' => '', 'sort_order' => 100],
                ],
            ],
            2 => [
                'state' => [
                    ['id' => 428, 'value' => 'Kuala Lumpur', 'description' => '', 'sort_order' => 100],
                    ['id' => 429, 'value' => 'Putrajaya', 'description' => '', 'sort_order' => 100],
                    ['id' => 430, 'value' => 'Labuan', 'description' => '', 'sort_order' => 100],
                ],
                'payment_method' => [
                    ['id' => 431, 'value' => 'Admin Approval', 'description' => '', 'sort_order' => 100],
                    ['id' => 432, 'value' => 'Cash', 'description' => '', 'sort_order' => 100],
                    ['id' => 433, 'value' => 'Online Banking', 'description' => '', 'sort_order' => 100],
                ],
            ],
            3 => [
                'property_type' => [
                    ['id' => 434, 'value' => 'Factory', 'description' => '', 'sort_order' => 100],
                    ['id' => 435, 'value' => 'Apartments / Condominium / Serviced Apartment / Townhouse', 'description' => '', 'sort_order' => 100],
                    ['id' => 436, 'value' => 'Flat', 'description' => '', 'sort_order' => 100],
                    ['id' => 437, 'value' => 'Retail Lot / Shop Lot ', 'description' => '', 'sort_order' => 100],
                    ['id' => 438, 'value' => 'Vacant Land', 'description' => '', 'sort_order' => 100],
                    ['id' => 439, 'value' => 'Building', 'description' => '', 'sort_order' => 100],
                    ['id' => 440, 'value' => 'Other', 'description' => '', 'sort_order' => 100],
                ],
            ],
        ];
    }
}
