<?php

namespace App\Http\Controllers\AccessControl;

use App\Http\Controllers\Controller;
use App\Http\Requests\AccessControl\StoreRoleRequest;
use App\Http\Requests\AccessControl\UpdateRoleRequest;
use App\Http\Resources\AccessControl\RoleResource;
use App\Models\Permission;
use App\Models\Role;
use App\Traits\QueryFilterableTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class RoleController extends Controller
{
    use QueryFilterableTrait;

    /**
     * Display a listing of the roles.
     *
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $this->authorize('viewAny', Role::class);

        $query = Role::query()
            ->withoutSuperAdmin()
            ->with(['permissions:id,name']);

        $this->applySearchFilter($query, $request);
        $this->applySorting($query, $request, 'level');

        $roles = $this->applyPagination($query, $request, 10,
            fn ($role) => (new RoleResource($role))->toArray($request));

        return Inertia::render('roles/Index', [
            'roles' => $roles,
            'filters' => $request->only(['name', 'per_page', 'sort_field', 'sort_direction']),
        ]);
    }

    /**
     * Show the form for creating a new role.
     *
     * @return \Inertia\Response
     */
    public function create()
    {
        $this->authorize('create', Role::class);

        return Inertia::render('roles/Create', [
            'permissions' => Permission::getForDropdown(),
        ]);
    }

    /**
     * Store a newly created role in storage.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(StoreRoleRequest $request)
    {
        $this->authorize('create', Role::class);

        try {
            DB::beginTransaction();

            $role = Role::create([
                'name' => $request->name,
                'level' => $request->level ?? 0,
                'can_see_same_level' => $request->can_see_same_level ?? false,
            ]);

            $role->syncPermissions($request->permissions);

            DB::commit();

            return redirect()->route('roles.index')->with('message', 'Role created successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create role: '.$e->getMessage());

            return redirect()->back()->withInput()->with('error', 'Failed to create role. '.$e->getMessage());
        }
    }

    /**
     * Display the specified role.
     *
     * @return \Inertia\Response
     */
    public function show(Role $role)
    {
        $this->authorize('view', $role);

        $role->load(['permissions:id,name']);

        return Inertia::render('roles/Show', [
            'role' => (new RoleResource($role))->toArray(request()),
        ]);
    }

    /**
     * Show the form for editing the specified role.
     *
     * @return \Inertia\Response
     */
    public function edit(Role $role)
    {
        $this->authorize('update', $role);

        return Inertia::render('roles/Edit', [
            'role' => [
                'id' => $role->id,
                'name' => $role->name,
                'level' => $role->level,
                'can_see_same_level' => (bool) $role->can_see_same_level,
                'permissions' => $role->permissions->pluck('id'),
            ],
            'permissions' => Permission::getForDropdown(),
        ]);
    }

    /**
     * Update the specified role in storage.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(UpdateRoleRequest $request, Role $role)
    {
        $this->authorize('update', $role);

        try {
            DB::beginTransaction();

            $role->update([
                'name' => $request->name,
                'level' => $request->level,
                'can_see_same_level' => $request->can_see_same_level ?? false,
            ]);

            $role->syncPermissions($request->permissions);

            DB::commit();

            return redirect()->route('roles.index')->with('message', 'Role updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update role: '.$e->getMessage());

            return redirect()->back()->withInput()->with('error', 'Failed to update role. '.$e->getMessage());
        }
    }

    /**
     * Remove the specified role from storage.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Role $role)
    {
        $this->authorize('delete', $role);

        try {
            $role->delete();

            return redirect()->route('roles.index')->with('message', 'Role deleted successfully');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to delete role. '.$e->getMessage());
        }
    }
}
