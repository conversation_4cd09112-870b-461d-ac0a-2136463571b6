<?php

namespace App\Http\Resources\Teams;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TeamResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $company = null;
        $headquarter = null;

        if ($teamCompany = $this->companies->first()) {
            $company = [
                'id' => $teamCompany->id,
                'display_name' => $teamCompany->is_headquarter ? null : $teamCompany->display_name,
                'code' => $teamCompany->code ?? '',
            ];

            $headquarter = [
                'id' => $teamCompany->headquarter->id,
                'display_name' => $teamCompany->headquarter->display_name,
                'code' => $teamCompany->headquarter->code ?? '',
            ];
        }

        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'code' => $this->code,
            'name' => $this->name,
            'email' => $this->email,
            'website' => $this->website,
            'status' => $this->status,
            'contact' => $this->contacts ? [
                'selection_country_id' => $this->telephone()->contactCountrySelection->value ?? null,
                'contact' => $this->telephone()->contact ?? null,
            ] : null,
            'address' => $this->address ? [
                'line_1' => $this->address->line_1,
                'line_2' => $this->address->line_2,
                'postcode' => $this->address->postcode,
                'city' => $this->address->city,
                'state' => $this->address->stateSelection->value ?? null,
                'country' => $this->address->countrySelection->value ?? null,
            ] : null,
            'company' => $company,
            'headquarter' => $headquarter,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'created_by' => $this->whenLoaded('createdBy', fn () => [
                'id' => $this->createdBy->id,
                'name' => $this->createdBy->username,
            ]),
            'updated_by' => $this->whenLoaded('updatedBy', fn () => [
                'id' => $this->updatedBy->id,
                'name' => $this->updatedBy->username,
            ]),
            'canUpdate' => $request->user()->can('update', $this->resource),
        ];
    }
}
