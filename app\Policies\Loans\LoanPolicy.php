<?php

namespace App\Policies\Loans;

use App\Enums\AccessControl\PermissionName;
use App\Models\Loan;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class LoanPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any loans.
     *
     * @return bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo(PermissionName::READ_LOANS->value);
    }

    /**
     * Determine whether the user can view the loan.
     *
     * @return bool
     */
    public function view(User $user, Loan $loan)
    {
        return $user->hasPermissionTo(PermissionName::READ_LOANS->value);
    }

    /**
     * Determine whether the user can create loans.
     *
     * @return bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo(PermissionName::CREATE_LOANS->value);
    }

    /**
     * Determine whether the user can update the loan.
     *
     * @return bool
     */
    public function update(User $user, Loan $loan)
    {
        if (! $user->hasCompanyAccess($loan->company_id)) {
            return false;
        }

        if ($user->isTeamAdminOrUser() && ! $user->hasTeamAccess($loan->team_id)) {
            return false;
        }

        return $user->hasPermissionTo(PermissionName::UPDATE_LOANS->value);
    }

    /**
     * Determine whether the user can delete the loan.
     *
     * @return bool
     */
    public function delete(User $user, Loan $loan)
    {
        return $user->hasPermissionTo(PermissionName::DELETE_LOANS->value);
    }

    /**
     * Determine whether the user can restore the loan.
     *
     * @return bool
     */
    public function restore(User $user, Loan $loan)
    {
        return $user->hasPermissionTo(PermissionName::DELETE_LOANS->value);
    }

    /**
     * Determine whether the user can permanently delete the loan.
     *
     * @return bool
     */
    public function forceDelete(User $user, Loan $loan)
    {
        return $user->hasPermissionTo(PermissionName::DELETE_LOANS->value);
    }
}
