<?php

namespace App\Enums\AccessControl;

use App\Traits\EnumTrait;

enum PermissionName: string
{
    use EnumTrait;

    // Dashboard permissions
    case READ_DASHBOARD = 'read dashboard';

    // Users permissions
    case READ_USERS = 'read users';
    case CREATE_USERS = 'create users';
    case UPDATE_USERS = 'update users';
    case DELETE_USERS = 'delete users';

    // Roles permissions
    case READ_ROLES = 'read roles';
    case CREATE_ROLES = 'create roles';
    case UPDATE_ROLES = 'update roles';
    case DELETE_ROLES = 'delete roles';

    // Permissions permissions
    case READ_PERMISSIONS = 'read permissions';
    case CREATE_PERMISSIONS = 'create permissions';
    case UPDATE_PERMISSIONS = 'update permissions';
    case DELETE_PERMISSIONS = 'delete permissions';

    // Headquarters permissions
    case READ_HEADQUARTERS = 'read headquarters';
    case CREATE_HEADQUARTERS = 'create headquarters';
    case UPDATE_HEADQUARTERS = 'update headquarters';
    case DELETE_HEADQUARTERS = 'delete headquarters';

    // Companies permissions
    case READ_COMPANIES = 'read companies';
    case CREATE_COMPANIES = 'create companies';
    case UPDATE_COMPANIES = 'update companies';
    case DELETE_COMPANIES = 'delete companies';

    // Teams permissions
    case READ_TEAMS = 'read teams';
    case CREATE_TEAMS = 'create teams';
    case UPDATE_TEAMS = 'update teams';
    case DELETE_TEAMS = 'delete teams';

    // Agent permissions
    case READ_AGENTS = 'read agents';
    case CREATE_AGENTS = 'create agents';
    case UPDATE_AGENTS = 'update agents';
    case DELETE_AGENTS = 'delete agents';

    // Agent Outcome Types permissions
    case READ_AGENT_OUTCOME_TYPES = 'read agent-outcome-types';
    case CREATE_AGENT_OUTCOME_TYPES = 'create agent-outcome-types';
    case UPDATE_AGENT_OUTCOME_TYPES = 'update agent-outcome-types';
    case DELETE_AGENT_OUTCOME_TYPES = 'delete agent-outcome-types';

    // Agent Outcomes permissions
    case READ_AGENT_OUTCOMES = 'read agent-outcomes';
    case CREATE_AGENT_OUTCOMES = 'create agent-outcomes';
    case UPDATE_AGENT_OUTCOMES = 'update agent-outcomes';
    case DELETE_AGENT_OUTCOMES = 'delete agent-outcomes';

    // Collaterals permissions
    case READ_COLLATERALS = 'read collaterals';
    case CREATE_COLLATERALS = 'create collaterals';
    case UPDATE_COLLATERALS = 'update collaterals';
    case DELETE_COLLATERALS = 'delete collaterals';

    // Customers permissions
    case READ_CUSTOMERS = 'read customers';
    case CREATE_CUSTOMERS = 'create customers';
    case UPDATE_CUSTOMERS = 'update customers';
    case DELETE_CUSTOMERS = 'delete customers';

    // Loans permissions
    case READ_LOANS = 'read loans';
    case CREATE_LOANS = 'create loans';
    case UPDATE_LOANS = 'update loans';
    case DELETE_LOANS = 'delete loans';

    // Read Loans Statuses permissions
    case DRAFT_READ_LOAN_STATUSES = 'draft read-loan-statuses';
    case PENDING_PROCESS_READ_LOAN_STATUSES = 'pending-process read-loan-statuses';
    case PENDING_REVIEW_READ_LOAN_STATUSES = 'pending-review read-loan-statuses';
    case PENDING_APPROVAL_READ_LOAN_STATUSES = 'pending-approval read-loan-statuses';
    case REJECTED_READ_LOAN_STATUSES = 'rejected read-loan-statuses';
    case APPROVED_READ_LOAN_STATUSES = 'approved read-loan-statuses';
    case CUSTOMER_REJECTED_READ_LOAN_STATUSES = 'customer-rejected read-loan-statuses';
    case CUSTOMER_APPROVED_READ_LOAN_STATUSES = 'customer-approved read-loan-statuses';
    case ON_GOING_READ_LOAN_STATUSES = 'on-going read-loan-statuses';
    case ON_GOING_OVERDUE_READ_LOAN_STATUSES = 'on-going-overdue read-loan-statuses';
    case COMPLETED_READ_LOAN_STATUSES = 'completed read-loan-statuses';
    case CANCELLED_READ_LOAN_STATUSES = 'cancelled read-loan-statuses';

    // Update Loans Statuses permissions
    case DRAFT_UPDATE_LOAN_STATUSES = 'draft update-loan-statuses';
    case PENDING_PROCESS_UPDATE_LOAN_STATUSES = 'pending-process update-loan-statuses';
    case PENDING_REVIEW_UPDATE_LOAN_STATUSES = 'pending-review update-loan-statuses';
    case PENDING_APPROVAL_UPDATE_LOAN_STATUSES = 'pending-approval update-loan-statuses';
    case REJECTED_UPDATE_LOAN_STATUSES = 'rejected update-loan-statuses';
    case APPROVED_UPDATE_LOAN_STATUSES = 'approved update-loan-statuses';
    case CUSTOMER_REJECTED_UPDATE_LOAN_STATUSES = 'customer-rejected update-loan-statuses';
    case CUSTOMER_APPROVED_UPDATE_LOAN_STATUSES = 'customer-approved update-loan-statuses';
    case ON_GOING_UPDATE_LOAN_STATUSES = 'on-going update-loan-statuses';
    case ON_GOING_OVERDUE_UPDATE_LOAN_STATUSES = 'on-going-overdue update-loan-statuses';
    case COMPLETED_UPDATE_LOAN_STATUSES = 'completed update-loan-statuses';
    case CANCELLED_UPDATE_LOAN_STATUSES = 'cancelled update-loan-statuses';

    // Loan Inquiries permissions
    case READ_LOAN_INQUIRIES = 'read loan-inquiries';

    // Locales permissions
    case READ_LOCALES = 'read locales';
    case CREATE_LOCALES = 'create locales';
    case UPDATE_LOCALES = 'update locales';
    case DELETE_LOCALES = 'delete locales';

    // Selections permissions
    case READ_SELECTIONS = 'read selections';
    case CREATE_SELECTIONS = 'create selections';
    case UPDATE_SELECTIONS = 'update selections';
    case DELETE_SELECTIONS = 'delete selections';

    // Audits permissions
    case READ_AUDITS = 'read audits';

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public static function criticalPermissions(): array
    {
        return [
            self::READ_PERMISSIONS->value,
            self::CREATE_PERMISSIONS->value,
            self::UPDATE_PERMISSIONS->value,
            self::DELETE_PERMISSIONS->value,
            self::READ_ROLES->value,
            self::CREATE_ROLES->value,
            self::UPDATE_ROLES->value,
            self::DELETE_ROLES->value,
        ];
    }

    public static function groupedByResource(): array
    {
        return [
            'dashboard' => [
                self::READ_DASHBOARD->value,
            ],
            'users' => [
                self::READ_USERS->value,
                self::CREATE_USERS->value,
                self::UPDATE_USERS->value,
                self::DELETE_USERS->value,
            ],
            'roles' => [
                self::READ_ROLES->value,
                self::CREATE_ROLES->value,
                self::UPDATE_ROLES->value,
                self::DELETE_ROLES->value,
            ],
            'permissions' => [
                self::READ_PERMISSIONS->value,
                self::CREATE_PERMISSIONS->value,
                self::UPDATE_PERMISSIONS->value,
                self::DELETE_PERMISSIONS->value,
            ],
            'headquarters' => [
                self::READ_HEADQUARTERS->value,
                self::CREATE_HEADQUARTERS->value,
                self::UPDATE_HEADQUARTERS->value,
                self::DELETE_HEADQUARTERS->value,
            ],
            'companies' => [
                self::READ_COMPANIES->value,
                self::CREATE_COMPANIES->value,
                self::UPDATE_COMPANIES->value,
                self::DELETE_COMPANIES->value,
            ],
            'teams' => [
                self::READ_TEAMS->value,
                self::CREATE_TEAMS->value,
                self::UPDATE_TEAMS->value,
                self::DELETE_TEAMS->value,
            ],
            'agents' => [
                self::READ_AGENTS->value,
                self::CREATE_AGENTS->value,
                self::UPDATE_AGENTS->value,
                self::DELETE_AGENTS->value,
            ],
            'agent-outcome-types' => [
                self::READ_AGENT_OUTCOME_TYPES->value,
                self::CREATE_AGENT_OUTCOME_TYPES->value,
                self::UPDATE_AGENT_OUTCOME_TYPES->value,
                self::DELETE_AGENT_OUTCOME_TYPES->value,
            ],
            'agent-outcomes' => [
                self::READ_AGENT_OUTCOMES->value,
                self::CREATE_AGENT_OUTCOMES->value,
                self::UPDATE_AGENT_OUTCOMES->value,
                self::DELETE_AGENT_OUTCOMES->value,
            ],
            'collaterals' => [
                self::READ_COLLATERALS->value,
                self::CREATE_COLLATERALS->value,
                self::UPDATE_COLLATERALS->value,
                self::DELETE_COLLATERALS->value,
            ],
            'customers' => [
                self::READ_CUSTOMERS->value,
                self::CREATE_CUSTOMERS->value,
                self::UPDATE_CUSTOMERS->value,
                self::DELETE_CUSTOMERS->value,
            ],
            'loans' => [
                self::READ_LOANS->value,
                self::CREATE_LOANS->value,
                self::UPDATE_LOANS->value,
                self::DELETE_LOANS->value,
            ],
            'read-loan-statuses' => [
                self::DRAFT_READ_LOAN_STATUSES->value,
                self::PENDING_PROCESS_READ_LOAN_STATUSES->value,
                self::PENDING_REVIEW_READ_LOAN_STATUSES->value,
                self::PENDING_APPROVAL_READ_LOAN_STATUSES->value,
                self::REJECTED_READ_LOAN_STATUSES->value,
                self::APPROVED_READ_LOAN_STATUSES->value,
                self::CUSTOMER_REJECTED_READ_LOAN_STATUSES->value,
                self::CUSTOMER_APPROVED_READ_LOAN_STATUSES->value,
                self::ON_GOING_READ_LOAN_STATUSES->value,
                self::ON_GOING_OVERDUE_READ_LOAN_STATUSES->value,
                self::COMPLETED_READ_LOAN_STATUSES->value,
                self::CANCELLED_READ_LOAN_STATUSES->value,
            ],
            'update-loan-statuses' => [
                self::DRAFT_UPDATE_LOAN_STATUSES->value,
                self::PENDING_PROCESS_UPDATE_LOAN_STATUSES->value,
                self::PENDING_REVIEW_UPDATE_LOAN_STATUSES->value,
                self::PENDING_APPROVAL_UPDATE_LOAN_STATUSES->value,
                self::REJECTED_UPDATE_LOAN_STATUSES->value,
                self::APPROVED_UPDATE_LOAN_STATUSES->value,
                self::CUSTOMER_REJECTED_UPDATE_LOAN_STATUSES->value,
                self::CUSTOMER_APPROVED_UPDATE_LOAN_STATUSES->value,
                self::ON_GOING_UPDATE_LOAN_STATUSES->value,
                self::ON_GOING_OVERDUE_UPDATE_LOAN_STATUSES->value,
                self::COMPLETED_UPDATE_LOAN_STATUSES->value,
                self::CANCELLED_UPDATE_LOAN_STATUSES->value,
            ],
            'loan-inquiries' => [
                self::READ_LOAN_INQUIRIES->value,
            ],
            'locales' => [
                self::READ_LOCALES->value,
                self::CREATE_LOCALES->value,
                self::UPDATE_LOCALES->value,
                self::DELETE_LOCALES->value,
            ],
            'selections' => [
                self::READ_SELECTIONS->value,
                self::CREATE_SELECTIONS->value,
                self::UPDATE_SELECTIONS->value,
                self::DELETE_SELECTIONS->value,
            ],
            'audits' => [
                self::READ_AUDITS->value,
            ],
        ];
    }

    public static function forRole(string $roleName): array
    {
        $basePermissions = [
            self::READ_DASHBOARD->value,
            self::READ_AGENTS->value, self::CREATE_AGENTS->value, self::UPDATE_AGENTS->value,
            self::READ_AGENT_OUTCOME_TYPES->value, self::CREATE_AGENT_OUTCOME_TYPES->value,
            self::UPDATE_AGENT_OUTCOME_TYPES->value, self::DELETE_AGENT_OUTCOME_TYPES->value,
            self::READ_AGENT_OUTCOMES->value, self::CREATE_AGENT_OUTCOMES->value,
            self::UPDATE_AGENT_OUTCOMES->value, self::DELETE_AGENT_OUTCOMES->value,
            self::READ_COLLATERALS->value, self::CREATE_COLLATERALS->value, self::UPDATE_COLLATERALS->value,
            self::READ_CUSTOMERS->value, self::CREATE_CUSTOMERS->value, self::UPDATE_CUSTOMERS->value,
            self::READ_LOANS->value, self::UPDATE_LOANS->value,
            self::READ_LOAN_INQUIRIES->value,
        ];

        $headquarterPermissions = [
            self::READ_USERS->value, self::CREATE_USERS->value, self::UPDATE_USERS->value,
            self::READ_COMPANIES->value, self::CREATE_COMPANIES->value, self::UPDATE_COMPANIES->value,
            self::READ_TEAMS->value,
        ];

        $headquarterAdminPermissions = [
            ...$headquarterPermissions,
            self::CREATE_TEAMS->value, self::UPDATE_TEAMS->value,
        ];

        $companyPermissions = [
            self::READ_USERS->value, self::CREATE_USERS->value, self::UPDATE_USERS->value,
            self::READ_TEAMS->value, self::CREATE_TEAMS->value, self::UPDATE_TEAMS->value,
        ];

        $teamPermissions = [
            self::READ_USERS->value, self::CREATE_USERS->value, self::UPDATE_USERS->value,
        ];

        // loan status permissions
        $baseLoanStatusPermissions = [
            self::PENDING_REVIEW_READ_LOAN_STATUSES->value,
            self::PENDING_APPROVAL_READ_LOAN_STATUSES->value,
            self::REJECTED_READ_LOAN_STATUSES->value,
            self::REJECTED_UPDATE_LOAN_STATUSES->value,
            self::APPROVED_READ_LOAN_STATUSES->value,
            self::CUSTOMER_REJECTED_READ_LOAN_STATUSES->value,
            self::CUSTOMER_REJECTED_UPDATE_LOAN_STATUSES->value,
            self::CUSTOMER_APPROVED_READ_LOAN_STATUSES->value,
            self::ON_GOING_READ_LOAN_STATUSES->value,
            self::ON_GOING_OVERDUE_READ_LOAN_STATUSES->value,
            self::COMPLETED_READ_LOAN_STATUSES->value,
        ];

        $officerLoanStatusPermissions = [...$baseLoanStatusPermissions,
            [
                self::DRAFT_READ_LOAN_STATUSES->value,
                self::DRAFT_UPDATE_LOAN_STATUSES->value,
                self::PENDING_PROCESS_READ_LOAN_STATUSES->value,
                self::PENDING_PROCESS_UPDATE_LOAN_STATUSES->value,
                self::CANCELLED_READ_LOAN_STATUSES->value,
                self::CANCELLED_UPDATE_LOAN_STATUSES->value,
                self::APPROVED_UPDATE_LOAN_STATUSES->value,
                self::CUSTOMER_APPROVED_UPDATE_LOAN_STATUSES->value,
                self::ON_GOING_UPDATE_LOAN_STATUSES->value,
                self::ON_GOING_OVERDUE_UPDATE_LOAN_STATUSES->value,
            ],
        ];

        $reviewerLoanStatusPermissions = [...$baseLoanStatusPermissions,
            [
                self::PENDING_REVIEW_UPDATE_LOAN_STATUSES->value,
            ],
        ];

        $approverLoanStatusPermissions = [...$baseLoanStatusPermissions,
            [
                self::PENDING_APPROVAL_UPDATE_LOAN_STATUSES->value,
            ],
        ];

        $adminLoanStatusPermissions = [...$baseLoanStatusPermissions, [
            ...$officerLoanStatusPermissions,
            ...$reviewerLoanStatusPermissions,
            ...$approverLoanStatusPermissions,
        ]];

        $createLoanPermissions = [
            self::CREATE_LOANS->value,
        ];

        return match ($roleName) {
            RoleName::HEADQUARTER_ADMIN->value => array_merge($basePermissions, $headquarterAdminPermissions, $adminLoanStatusPermissions),
            RoleName::HEADQUARTER_LOAN_OFFICER->value => array_merge($basePermissions, $headquarterPermissions, $officerLoanStatusPermissions, $createLoanPermissions),
            RoleName::HEADQUARTER_LOAN_REVIEWER->value => array_merge($basePermissions, $headquarterPermissions, $reviewerLoanStatusPermissions),
            RoleName::HEADQUARTER_LOAN_APPROVER->value => array_merge($basePermissions, $headquarterPermissions, $approverLoanStatusPermissions),
            RoleName::COMPANY_ADMIN->value => array_merge($basePermissions, $companyPermissions, $adminLoanStatusPermissions),
            RoleName::TEAM_ADMIN->value => array_merge($basePermissions, $teamPermissions, $adminLoanStatusPermissions),
            RoleName::TEAM_LOAN_OFFICER->value => array_merge($basePermissions, $officerLoanStatusPermissions, $createLoanPermissions),
            RoleName::TEAM_LOAN_REVIEWER->value => array_merge($basePermissions, $reviewerLoanStatusPermissions),
            RoleName::TEAM_LOAN_APPROVER->value => array_merge($basePermissions, $approverLoanStatusPermissions),
            default => [],
        };
    }
}
