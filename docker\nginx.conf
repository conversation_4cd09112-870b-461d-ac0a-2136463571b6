server {
    listen 80;
    server_name _;
    root /var/www/public;

    index index.php index.html;

    client_max_body_size 11M;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ ^/up$ {
        # Restrict access to localhost or internal IPs
        allow 10.0.0.0/16;
        allow ********/16;
        allow ********/16;
        allow 127.0.0.1;
        deny all;

        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        include fastcgi_params;
        fastcgi_pass 127.0.0.1:9000;  # default FPM port
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        fastcgi_param DOCUMENT_ROOT $realpath_root;
        fastcgi_param HTTPS on;
        fastcgi_param HTTP_X_FORWARDED_PROTO https;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
